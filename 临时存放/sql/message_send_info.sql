/*
 Navicat Premium Dump SQL

 Source Server         : CBH-************-3306-MySQL
 Source Server Type    : MySQL
 Source Server Version : 50729 (5.7.29-log)
 Source Host           : ************:3306
 Source Schema         : crm_wechat

 Target Server Type    : MySQL
 Target Server Version : 50729 (5.7.29-log)
 File Encoding         : 65001

 Date: 16/07/2025 13:21:46
*/

SET NAMES utf8mb4;
SET FOREIGN_KEY_CHECKS = 0;

-- ----------------------------
-- Table structure for message_send_info
-- ----------------------------
DROP TABLE IF EXISTS `message_send_info`;
CREATE TABLE `message_send_info`  (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '主键id',
  `accept_id` int(11) NOT NULL COMMENT '接收消息id',
  `send_dt` varchar(8) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '发送日期',
  `templete_id` varchar(24) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '模板id',
  `send_channel` varchar(2) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '消息发送通道|方式， 1-企微群机器发送 ',
  `TEMPLETE_PARAMS` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '模板参数',
  `send_status` varchar(2) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '发送状态， 1-未推送；2-推送中；3-推送成功；4-推送失败；5-重新推送',
  `send_times` int(2) NULL DEFAULT NULL COMMENT '消息发送次数',
  `response_code` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '接口返回码',
  `RESPONSE_MSG` varchar(4000) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '接口返回描述',
  `create_time` datetime NOT NULL COMMENT '创建时间',
  `update_time` datetime NULL DEFAULT NULL COMMENT '更新时间',
  `message_uuid` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '该条消息在下游消息中心中的UUID',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `idx_send_status`(`send_status`) USING BTREE,
  INDEX `idx_message_send_info_message_uuid`(`message_uuid`) USING BTREE,
  INDEX `idx_message_send_info_accept_id`(`accept_id`) USING BTREE,
  INDEX `idx_update_time`(`update_time`) USING BTREE,
  INDEX `idx_create_time`(`create_time`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 139299 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '消息发送表' ROW_FORMAT = Dynamic;

SET FOREIGN_KEY_CHECKS = 1;
