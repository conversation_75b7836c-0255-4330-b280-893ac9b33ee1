<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.howbuy.crm.wechat.dao.mapper.CmWechatCompanyMapper">
  <resultMap id="BaseResultMap" type="com.howbuy.crm.wechat.dao.po.CmWechatCompanyPO">
    <!--@mbg.generated-->
    <!--@Table cm_wechat_company-->
    <id column="COMPANY_NO" jdbcType="VARCHAR" property="companyNo" />
    <result column="COMPANY_DESC" jdbcType="VARCHAR" property="companyDesc" />
    <result column="CORP_ID" jdbcType="VARCHAR" property="corpId" />
    <result column="TOKEN" jdbcType="VARCHAR" property="token" />
    <result column="ENCODING_AES_KEY" jdbcType="VARCHAR" property="encodingAesKey" />
    <result column="CREATOR" jdbcType="VARCHAR" property="creator" />
    <result column="CREATE_TIME" jdbcType="TIMESTAMP" property="createTime" />
    <result column="MODIFIER" jdbcType="VARCHAR" property="modifier" />
    <result column="MODIFY_TIME" jdbcType="TIMESTAMP" property="modifyTime" />
    <result column="REC_STAT" jdbcType="VARCHAR" property="recStat" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    COMPANY_NO, COMPANY_DESC, CORP_ID, TOKEN, ENCODING_AES_KEY, CREATOR, CREATE_TIME, 
    MODIFIER, MODIFY_TIME, REC_STAT
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.String" resultMap="BaseResultMap">
    <!--@mbg.generated-->
    select 
    <include refid="Base_Column_List" />
    from cm_wechat_company
    where COMPANY_NO = #{companyNo,jdbcType=VARCHAR}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.String">
    <!--@mbg.generated-->
    delete from cm_wechat_company
    where COMPANY_NO = #{companyNo,jdbcType=VARCHAR}
  </delete>
  <insert id="insert" parameterType="com.howbuy.crm.wechat.dao.po.CmWechatCompanyPO">
    <!--@mbg.generated-->
    insert into cm_wechat_company (COMPANY_NO, COMPANY_DESC, CORP_ID, 
      TOKEN, ENCODING_AES_KEY, CREATOR, 
      CREATE_TIME, MODIFIER, MODIFY_TIME, 
      REC_STAT)
    values (#{companyNo,jdbcType=VARCHAR}, #{companyDesc,jdbcType=VARCHAR}, #{corpId,jdbcType=VARCHAR}, 
      #{token,jdbcType=VARCHAR}, #{encodingAesKey,jdbcType=VARCHAR}, #{creator,jdbcType=VARCHAR}, 
      #{createTime,jdbcType=TIMESTAMP}, #{modifier,jdbcType=VARCHAR}, #{modifyTime,jdbcType=TIMESTAMP}, 
      #{recStat,jdbcType=VARCHAR})
  </insert>
  <insert id="insertSelective" parameterType="com.howbuy.crm.wechat.dao.po.CmWechatCompanyPO">
    <!--@mbg.generated-->
    insert into cm_wechat_company
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="companyNo != null">
        COMPANY_NO,
      </if>
      <if test="companyDesc != null">
        COMPANY_DESC,
      </if>
      <if test="corpId != null">
        CORP_ID,
      </if>
      <if test="token != null">
        TOKEN,
      </if>
      <if test="encodingAesKey != null">
        ENCODING_AES_KEY,
      </if>
      <if test="creator != null">
        CREATOR,
      </if>
      <if test="createTime != null">
        CREATE_TIME,
      </if>
      <if test="modifier != null">
        MODIFIER,
      </if>
      <if test="modifyTime != null">
        MODIFY_TIME,
      </if>
      <if test="recStat != null">
        REC_STAT,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="companyNo != null">
        #{companyNo,jdbcType=VARCHAR},
      </if>
      <if test="companyDesc != null">
        #{companyDesc,jdbcType=VARCHAR},
      </if>
      <if test="corpId != null">
        #{corpId,jdbcType=VARCHAR},
      </if>
      <if test="token != null">
        #{token,jdbcType=VARCHAR},
      </if>
      <if test="encodingAesKey != null">
        #{encodingAesKey,jdbcType=VARCHAR},
      </if>
      <if test="creator != null">
        #{creator,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="modifier != null">
        #{modifier,jdbcType=VARCHAR},
      </if>
      <if test="modifyTime != null">
        #{modifyTime,jdbcType=TIMESTAMP},
      </if>
      <if test="recStat != null">
        #{recStat,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.howbuy.crm.wechat.dao.po.CmWechatCompanyPO">
    <!--@mbg.generated-->
    update cm_wechat_company
    <set>
      <if test="companyDesc != null">
        COMPANY_DESC = #{companyDesc,jdbcType=VARCHAR},
      </if>
      <if test="corpId != null">
        CORP_ID = #{corpId,jdbcType=VARCHAR},
      </if>
      <if test="token != null">
        TOKEN = #{token,jdbcType=VARCHAR},
      </if>
      <if test="encodingAesKey != null">
        ENCODING_AES_KEY = #{encodingAesKey,jdbcType=VARCHAR},
      </if>
      <if test="creator != null">
        CREATOR = #{creator,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        CREATE_TIME = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="modifier != null">
        MODIFIER = #{modifier,jdbcType=VARCHAR},
      </if>
      <if test="modifyTime != null">
        MODIFY_TIME = #{modifyTime,jdbcType=TIMESTAMP},
      </if>
      <if test="recStat != null">
        REC_STAT = #{recStat,jdbcType=VARCHAR},
      </if>
    </set>
    where COMPANY_NO = #{companyNo,jdbcType=VARCHAR}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.howbuy.crm.wechat.dao.po.CmWechatCompanyPO">
    <!--@mbg.generated-->
    update cm_wechat_company
    set COMPANY_DESC = #{companyDesc,jdbcType=VARCHAR},
      CORP_ID = #{corpId,jdbcType=VARCHAR},
      TOKEN = #{token,jdbcType=VARCHAR},
      ENCODING_AES_KEY = #{encodingAesKey,jdbcType=VARCHAR},
      CREATOR = #{creator,jdbcType=VARCHAR},
      CREATE_TIME = #{createTime,jdbcType=TIMESTAMP},
      MODIFIER = #{modifier,jdbcType=VARCHAR},
      MODIFY_TIME = #{modifyTime,jdbcType=TIMESTAMP},
      REC_STAT = #{recStat,jdbcType=VARCHAR}
    where COMPANY_NO = #{companyNo,jdbcType=VARCHAR}
  </update>


  <select id="selectConfigByCorpId" parameterType="string" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from cm_wechat_company
    where CORP_ID = #{corpId,jdbcType=VARCHAR}
    AND REC_STAT = '1'
  </select>

  <select id="selectConfigByCompanyNo" parameterType="string" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from cm_wechat_company
    where COMPANY_NO = #{companyNo,jdbcType=VARCHAR}
    AND REC_STAT = '1'
  </select>

  <select id="selectConfigList" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from cm_wechat_company
    where REC_STAT = '1'
  </select>
</mapper>