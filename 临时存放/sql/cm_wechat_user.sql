/*
 Navicat Premium Dump SQL

 Source Server         : CBH-************-3306-MySQL
 Source Server Type    : MySQL
 Source Server Version : 50729 (5.7.29-log)
 Source Host           : ************:3306
 Source Schema         : crm_wechat

 Target Server Type    : MySQL
 Target Server Version : 50729 (5.7.29-log)
 File Encoding         : 65001

 Date: 16/07/2025 13:21:22
*/

SET NAMES utf8mb4;
SET FOREIGN_KEY_CHECKS = 0;

-- ----------------------------
-- Table structure for cm_wechat_user
-- ----------------------------
DROP TABLE IF EXISTS `cm_wechat_user`;
CREATE TABLE `cm_wechat_user`  (
  `USER_ID` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '用户ID',
  `FLAG` char(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '标识 0 可用 1 删除',
  `CREATER` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '创建人',
  `CREATE_TIME` datetime(6) NULL DEFAULT NULL COMMENT '创建时间',
  `MODIFIER` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '修改人',
  `UPDATE_TIME` datetime(6) NULL DEFAULT NULL COMMENT '修改时间',
  PRIMARY KEY (`USER_ID`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '企业微信用户配置表' ROW_FORMAT = DYNAMIC;

SET FOREIGN_KEY_CHECKS = 1;
