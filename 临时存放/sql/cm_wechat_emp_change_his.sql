/*
 Navicat Premium Dump SQL

 Source Server         : CBH-************-3306-MySQL
 Source Server Type    : MySQL
 Source Server Version : 50729 (5.7.29-log)
 Source Host           : ************:3306
 Source Schema         : crm_wechat

 Target Server Type    : MySQL
 Target Server Version : 50729 (5.7.29-log)
 File Encoding         : 65001

 Date: 16/07/2025 13:20:19
*/

SET NAMES utf8mb4;
SET FOREIGN_KEY_CHECKS = 0;

-- ----------------------------
-- Table structure for cm_wechat_emp_change_his
-- ----------------------------
DROP TABLE IF EXISTS `cm_wechat_emp_change_his`;
CREATE TABLE `cm_wechat_emp_change_his`  (
  `ID` bigint(20) NOT NULL DEFAULT 0 COMMENT '主键',
  `EMP_ID` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '员工的userId',
  `BEFORE_DEPT_ID` int(11) NULL DEFAULT NULL COMMENT '变更前的部门id',
  `AFTER_DEPT_ID` int(11) NULL DEFAULT NULL COMMENT '变更后的部门id',
  `CHANGE_TIME` datetime(6) NULL DEFAULT NULL COMMENT '变更时间',
  `COMPANY_NO` varchar(2) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '企业编码1好买财富2好买基金',
  PRIMARY KEY (`ID`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '员工变更记录表' ROW_FORMAT = DYNAMIC;

SET FOREIGN_KEY_CHECKS = 1;
