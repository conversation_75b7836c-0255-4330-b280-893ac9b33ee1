/*
 Navicat Premium Dump SQL

 Source Server         : CBH-************-3306-MySQL
 Source Server Type    : MySQL
 Source Server Version : 50729 (5.7.29-log)
 Source Host           : ************:3306
 Source Schema         : crm_wechat

 Target Server Type    : MySQL
 Target Server Version : 50729 (5.7.29-log)
 File Encoding         : 65001

 Date: 16/07/2025 13:21:29
*/

SET NAMES utf8mb4;
SET FOREIGN_KEY_CHECKS = 0;

-- ----------------------------
-- Table structure for cm_wechat_voice_remind
-- ----------------------------
DROP TABLE IF EXISTS `cm_wechat_voice_remind`;
CREATE TABLE `cm_wechat_voice_remind`  (
  `ID` bigint(20) NOT NULL DEFAULT 0 COMMENT '主键,原Voiceid',
  `ACCEPT_USER_ID` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '消息接收人',
  `MOBILE` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '主叫号码',
  `REMIND_TIME` datetime(6) NULL DEFAULT NULL COMMENT '消息事件',
  `CREATE_TIME` datetime(6) NULL DEFAULT NULL COMMENT '消息创建时间',
  `REMIND_TYPE` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '消息类型',
  PRIMARY KEY (`ID`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '语音拨号后消息记录操作表' ROW_FORMAT = DYNAMIC;

SET FOREIGN_KEY_CHECKS = 1;
