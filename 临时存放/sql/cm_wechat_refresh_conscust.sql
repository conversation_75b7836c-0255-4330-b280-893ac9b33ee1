/*
 Navicat Premium Dump SQL

 Source Server         : CBH-************-3306-MySQL
 Source Server Type    : MySQL
 Source Server Version : 50729 (5.7.29-log)
 Source Host           : ************:3306
 Source Schema         : crm_wechat

 Target Server Type    : MySQL
 Target Server Version : 50729 (5.7.29-log)
 File Encoding         : 65001

 Date: 16/07/2025 13:21:02
*/

SET NAMES utf8mb4;
SET FOREIGN_KEY_CHECKS = 0;

-- ----------------------------
-- Table structure for cm_wechat_refresh_conscust
-- ----------------------------
DROP TABLE IF EXISTS `cm_wechat_refresh_conscust`;
CREATE TABLE `cm_wechat_refresh_conscust`  (
  `ID` int(11) NOT NULL AUTO_INCREMENT COMMENT '主键',
  `DEAL_DATA` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '处理数据',
  `DEAL_DATE` varchar(10) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '处理时间YYYYMMDD',
  `DEAL_TYPE` varchar(2) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '处理类型1投顾客户列表刷新2客户姓名备注刷新',
  `DEAL_STATUS` varchar(2) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '处理状态1已处理0未处理2处理完废弃',
  `CREATOR` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '创建人',
  `CREATE_TIME` datetime(6) NULL DEFAULT NULL COMMENT '创建时间',
  `MODIFIER` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '更新人',
  `UPDATE_TIME` datetime(6) NULL DEFAULT NULL COMMENT '更新时间',
  PRIMARY KEY (`ID`) USING BTREE,
  INDEX `idx_deal_date`(`DEAL_DATE`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 377649 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '投顾客户微信刷新状态表' ROW_FORMAT = DYNAMIC;

SET FOREIGN_KEY_CHECKS = 1;
