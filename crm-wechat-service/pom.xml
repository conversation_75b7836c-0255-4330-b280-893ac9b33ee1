<?xml version='1.0' encoding='utf-8'?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <artifactId>crm-wechat</artifactId>
        <groupId>com.howbuy.crm</groupId>
        <version>2.0.3.3-RELEASE</version>
    </parent>
    <modelVersion>4.0.0</modelVersion>

    <name>crm-wechat-service</name>
    <artifactId>crm-wechat-service</artifactId>

    <properties>
        <argLine>-Dfile.encoding=UTF-8</argLine>
        <com.howbuy.howbuy_dfile.version>1.18.1-RELEASE</com.howbuy.howbuy_dfile.version>
        <com.howbuy.howbuy-cachemanagement.version>3.5.1-RELEASE</com.howbuy.howbuy-cachemanagement.version>
        <com.howbuy.message-public-client.version>5.1.12-RELEASE</com.howbuy.message-public-client.version>
        <com.howbuy.howbuy-ccms-watcher.version>6.0.1-RELEASE</com.howbuy.howbuy-ccms-watcher.version>
        <com.howbuy.howbuy-message-service-2.version>2.1.4-RELEASE</com.howbuy.howbuy-message-service-2.version>
        <com.howbuy.howbuy-message-rocket-2.version>2.1.4-RELEASE</com.howbuy.howbuy-message-rocket-2.version>
        <com.howbuy.howbuy-message-amq-2.version>2.1.4-RELEASE</com.howbuy.howbuy-message-amq-2.version>
        <com.howbuy.crm-nt-client.version>1.8.2.2-RELEASE</com.howbuy.crm-nt-client.version>
        <com.howbuy.crm-core-client.version>1.8.2.2-RELEASE</com.howbuy.crm-core-client.version>
        <com.howbuy.acc-center-facade.version>3.2.6-RELEASE</com.howbuy.acc-center-facade.version>
         <com.howbuy.crm-account.version>bugfix20250711c-RELEASE</com.howbuy.crm-account.version>
</properties>



    <!--所有maven依赖 新增的时候 必须检查是否有冲突, 需要将冲突解决掉, 将依赖保持干净的状态 因为单元测试引入的可以不用处理-->
    <dependencies>
        <dependency>
            <groupId>com.howbuy.crm</groupId>
            <artifactId>crm-account-client</artifactId>
            <version>${com.howbuy.crm-account.version}</version>
        </dependency>
        <dependency>
            <groupId>commons-codec</groupId>
            <artifactId>commons-codec</artifactId>
        </dependency>

        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-web</artifactId>
            <exclusions>
                <exclusion>
                    <artifactId>spring-boot-starter-logging</artifactId>
                    <groupId>org.springframework.boot</groupId>
                </exclusion>
                <!-- 排除自带的logback依赖 -->
                <exclusion>
                    <groupId>org.springframework.boot</groupId>
                    <artifactId>spring-boot-starter-logging</artifactId>
                </exclusion>
            </exclusions>
        </dependency>

        <!-- 日志脱敏 -->
        <dependency>
            <groupId>com.howbuy.tms</groupId>
            <artifactId>tms-common-log-pattern</artifactId>
        </dependency>

        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-validation</artifactId>
        </dependency>

        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-log4j2</artifactId>
        </dependency>

        <dependency>
            <groupId>org.mybatis.spring.boot</groupId>
            <artifactId>mybatis-spring-boot-starter</artifactId>
            <exclusions>
                <exclusion>
                    <artifactId>spring-boot-starter-logging</artifactId>
                    <groupId>org.springframework.boot</groupId>
                </exclusion>
            </exclusions>
        </dependency>

        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-devtools</artifactId>
            <scope>runtime</scope>
            <optional>true</optional>
        </dependency>

        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-test</artifactId>
            <scope>test</scope>
        </dependency>


        <!--spring cloud Alibaba-->
        <dependency>
            <groupId>com.alibaba.cloud</groupId>
            <artifactId>spring-cloud-starter-alibaba-nacos-config</artifactId>
            <exclusions>
                <exclusion>
                    <artifactId>commons-io</artifactId>
                    <groupId>commons-io</groupId>
                </exclusion>
            </exclusions>
        </dependency>

        <dependency>
            <groupId>com.alibaba.cloud</groupId>
            <artifactId>spring-cloud-starter-alibaba-nacos-discovery</artifactId>
            <exclusions>
                <exclusion>
                    <artifactId>jsr305</artifactId>
                    <groupId>com.google.code.findbugs</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>spring-cloud-starter-netflix-ribbon</artifactId>
                    <groupId>org.springframework.cloud</groupId>
                </exclusion>
            </exclusions>
        </dependency>

        <dependency>
            <groupId>org.springframework.cloud</groupId>
            <artifactId>spring-cloud-starter-openfeign</artifactId>
            <exclusions>
                <exclusion>
                    <groupId>com.google.guava</groupId>
                    <artifactId>guava</artifactId>
                </exclusion>
                <exclusion>
                    <artifactId>commons-configuration</artifactId>
                    <groupId>commons-configuration</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>jsr305</artifactId>
                    <groupId>com.google.code.findbugs</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>commons-io</artifactId>
                    <groupId>commons-io</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>spring-cloud-netflix-ribbon</artifactId>
                    <groupId>org.springframework.cloud</groupId>
                </exclusion>
            </exclusions>
        </dependency>

        <dependency>
            <groupId>io.github.openfeign</groupId>
            <artifactId>feign-okhttp</artifactId>
        </dependency>

        <dependency>
            <groupId>org.springframework.cloud</groupId>
            <artifactId>spring-cloud-loadbalancer</artifactId>
        </dependency>

        <dependency>
            <groupId>com.github.ben-manes.caffeine</groupId>
            <artifactId>caffeine</artifactId>
        </dependency>

        <dependency>
            <groupId>org.apache.logging.log4j</groupId>
            <artifactId>log4j-core</artifactId>
            <exclusions>
                <exclusion>
                    <artifactId>log4j-api</artifactId>
                    <groupId>org.apache.logging.log4j</groupId>
                </exclusion>
            </exclusions>
        </dependency>

        <dependency>
            <groupId>org.apache.logging.log4j</groupId>
            <artifactId>log4j-api</artifactId>
        </dependency>

        <!--lombok-->
        <dependency>
            <groupId>org.projectlombok</groupId>
            <artifactId>lombok</artifactId>
        </dependency>
        <!-- log4j slf4j 桥接 -->
        <dependency>
            <groupId>org.slf4j</groupId>
            <artifactId>log4j-over-slf4j</artifactId>
        </dependency>

        <dependency>
            <groupId>com.lmax</groupId>
            <artifactId>disruptor</artifactId>
        </dependency>

        <dependency>
            <groupId>org.apache.dubbo</groupId>
            <artifactId>dubbo-spring-boot-starter</artifactId>
        </dependency>

        <dependency>
            <groupId>org.apache.commons</groupId>
            <artifactId>commons-lang3</artifactId>
        </dependency>

        <dependency>
            <groupId>org.apache.commons</groupId>
            <artifactId>commons-collections4</artifactId>
        </dependency>

        <dependency>
            <groupId>org.apache.dubbo</groupId>
            <artifactId>dubbo-dependencies-zookeeper</artifactId>
            <type>pom</type>
            <exclusions>
                <exclusion>
                    <groupId>org.slf4j</groupId>
                    <artifactId>slf4j-log4j12</artifactId>
                </exclusion>
                <exclusion>
                    <artifactId>log4j</artifactId>
                    <groupId>log4j</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>guava</artifactId>
                    <groupId>com.google.guava</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>netty</artifactId>
                    <groupId>io.netty</groupId>
                </exclusion>
            </exclusions>
        </dependency>

        <dependency>
            <groupId>com.alibaba.spring</groupId>
            <artifactId>spring-context-support</artifactId>
        </dependency>

        <dependency>
            <groupId>com.alibaba</groupId>
            <artifactId>druid</artifactId>
        </dependency>

        <!-- 数据库-->
        <dependency>
            <groupId>com.oracle.database.jdbc</groupId>
            <artifactId>ojdbc8</artifactId>
            <scope>runtime</scope>
        </dependency>

        <dependency>
            <groupId>com.github.pagehelper</groupId>
            <artifactId>pagehelper</artifactId>
        </dependency>

        <dependency>
            <groupId>com.alibaba</groupId>
            <artifactId>fastjson</artifactId>
        </dependency>

        <dependency>
            <groupId>mysql</groupId>
            <artifactId>mysql-connector-java</artifactId>
            <scope>runtime</scope>
        </dependency>

        <!--其他业务组件依赖 自行添加-->
        <!--本项目依赖-->
        <dependency>
            <groupId>com.howbuy.crm</groupId>
            <artifactId>crm-wechat-dao</artifactId>
        </dependency>
        <dependency>
            <groupId>com.howbuy.crm</groupId>
            <artifactId>crm-wechat-client</artifactId>
        </dependency>


        <!--howbuy-->
        <dependency>
            <groupId>com.howbuy</groupId>
            <artifactId>howbuy-cachemanagement</artifactId>
            <version>${com.howbuy.howbuy-cachemanagement.version}</version>
            <exclusions>
                <exclusion>
                    <artifactId>commons-logging</artifactId>
                    <groupId>commons-logging</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>javassist</artifactId>
                    <groupId>org.javassist</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>commons-beanutils</artifactId>
                    <groupId>commons-beanutils</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>commons-collections</artifactId>
                    <groupId>commons-collections</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>commons-lang</artifactId>
                    <groupId>commons-lang</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>activemq-all</artifactId>
                    <groupId>org.apache.activemq</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>slf4j-log4j12</artifactId>
                    <groupId>org.slf4j</groupId>
                </exclusion>
                <exclusion>
                    <groupId>com.howbuy</groupId>
                    <artifactId>howbuy-message-client-1</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>com.howbuy</groupId>
                    <artifactId>howbuy-message-amq-1</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>com.howbuy</groupId>
                    <artifactId>howbuy-message-service-1</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>com.howbuy</groupId>
                    <artifactId>howbuy-message-amq-2</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>com.howbuy</groupId>
                    <artifactId>howbuy-message-client-2</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>com.howbuy</groupId>
                    <artifactId>howbuy-message-service-2</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>redis.clients</groupId>
                    <artifactId>jedis</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>com.google.guava</groupId>
                    <artifactId>guava</artifactId>
                </exclusion>
                <exclusion>
                    <artifactId>log4j</artifactId>
                    <groupId>log4j</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>howbuy-ccms-independent</artifactId>
                    <groupId>com.howbuy</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>howbuy-message-amq</artifactId>
                    <groupId>com.howbuy</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>howbuy-message-service</artifactId>
                    <groupId>com.howbuy</groupId>
                </exclusion>
            </exclusions>
        </dependency>

        <dependency>
            <groupId>redis.clients</groupId>
            <artifactId>jedis</artifactId>
        </dependency>

        <dependency>
            <groupId>com.howbuy</groupId>
            <artifactId>utils</artifactId>
            <exclusions>
                <exclusion>
                    <groupId>*</groupId>
                    <artifactId>*</artifactId>
                </exclusion>
            </exclusions>
        </dependency>

        <dependency>
            <groupId>com.howbuy</groupId>
            <artifactId>howbuy-ccms-watcher</artifactId>
            <version>${com.howbuy.howbuy-ccms-watcher.version}</version>
        </dependency>

      <!--MQ消息 from PA-->
        <dependency>
            <groupId>com.howbuy</groupId>
            <artifactId>howbuy-message-service-2</artifactId>
            <version>${com.howbuy.howbuy-message-service-2.version}</version>
            <exclusions>
                <exclusion>
                    <artifactId>slf4j-log4j12</artifactId>
                    <groupId>org.slf4j</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>slf4j-api</artifactId>
                    <groupId>org.slf4j</groupId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>com.howbuy</groupId>
            <artifactId>howbuy-message-amq-2</artifactId>
            <version>${com.howbuy.howbuy-message-amq-2.version}</version>
            <exclusions>
                <exclusion>
                    <artifactId>slf4j-log4j12</artifactId>
                    <groupId>org.slf4j</groupId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>com.howbuy</groupId>
            <artifactId>howbuy-message-rocket-2</artifactId>
            <version>${com.howbuy.howbuy-message-rocket-2.version}</version>
            <exclusions>
                <exclusion>
                    <artifactId>slf4j-log4j12</artifactId>
                    <groupId>org.slf4j</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>slf4j-api</artifactId>
                    <groupId>org.slf4j</groupId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>com.howbuy.pa</groupId>
            <artifactId>ccms-watcher-plugin-nacosImpl</artifactId>
            <version>1.0.0-RELEASE</version>
            <exclusions>
                <exclusion>
                    <artifactId>guava</artifactId>
                    <groupId>com.google.guava</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>slf4j-api</artifactId>
                    <groupId>org.slf4j</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>nacos-client</artifactId>
                    <groupId>com.alibaba.nacos</groupId>
                </exclusion>
            </exclusions>
        </dependency>
        <!--MQ消息 from PA-->



        <!--引入dfile的依赖-->
        <dependency>
            <groupId>com.howbuy.dfile</groupId>
            <artifactId>howbuy-dfile-service</artifactId>
            <version>${com.howbuy.howbuy_dfile.version}</version>
            <exclusions>
                <exclusion>
                    <artifactId>logback-classic</artifactId>
                    <groupId>ch.qos.logback</groupId>
                </exclusion>
            </exclusions>
        </dependency>
       <!-- <dependency>
            <groupId>com.howbuy.dfile</groupId>
            <artifactId>howbuy-dfile-impl-local</artifactId>
            <version>${com.howbuy.howbuy_dfile.version}</version>
        </dependency>-->
        <dependency>
            <groupId>com.howbuy.dfile</groupId>
            <artifactId>howbuy-dfile-impl-webdav</artifactId>
            <version>${com.howbuy.howbuy_dfile.version}</version>
        </dependency>
        <!--引入dfile的依赖-->

        <dependency>
            <groupId>com.howbuy.acccenter</groupId>
            <artifactId>acc-center-facade</artifactId>
            <version>${com.howbuy.acc-center-facade.version}</version>
        </dependency>

        <dependency>
            <groupId>com.thoughtworks.xstream</groupId>
            <artifactId>xstream</artifactId>
            <version>${xstream.version}</version>
        </dependency>
        <dependency>
            <groupId>com.howbuy.crm</groupId>
            <artifactId>crm-nt-client</artifactId>
            <version>${com.howbuy.crm-nt-client.version}</version>
        </dependency>
        <dependency>
            <groupId>com.howbuy.crm</groupId>
            <artifactId>crm-core-client</artifactId>
            <version>${com.howbuy.crm-core-client.version}</version>
        </dependency>

        <dependency>
            <groupId>com.howbuy.cc.message</groupId>
            <artifactId>message-public-client</artifactId>
            <version>${com.howbuy.message-public-client.version}</version>
        </dependency>


        <dependency>
            <groupId>com.google.code.gson</groupId>
            <artifactId>gson</artifactId>
            <version>2.8.6</version>
        </dependency>
    </dependencies>

</project>