<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.howbuy.crm.wechat.dao.mapper.CmWechatApplicationMapper">
  <resultMap id="BaseResultMap" type="com.howbuy.crm.wechat.dao.po.CmWechatApplicationPO">
    <!--@mbg.generated-->
    <!--@Table cm_wechat_application-->
    <id column="APPLICATION_CODE" jdbcType="VARCHAR" property="applicationCode" />
    <result column="APPLICATION_DESC" jdbcType="VARCHAR" property="applicationDesc" />
    <result column="COMPANY_NO" jdbcType="VARCHAR" property="companyNo" />
    <result column="ACCESS_SECRET" jdbcType="VARCHAR" property="accessSecret" />
    <result column="AGENT_ID" jdbcType="VARCHAR" property="agentId" />
    <result column="CREATOR" jdbcType="VARCHAR" property="creator" />
    <result column="CREATE_TIME" jdbcType="TIMESTAMP" property="createTime" />
    <result column="MODIFIER" jdbcType="VARCHAR" property="modifier" />
    <result column="MODIFY_TIME" jdbcType="TIMESTAMP" property="modifyTime" />
    <result column="REC_STAT" jdbcType="VARCHAR" property="recStat" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    APPLICATION_CODE, APPLICATION_DESC, COMPANY_NO, ACCESS_SECRET, AGENT_ID, CREATOR, 
    CREATE_TIME, MODIFIER, MODIFY_TIME, REC_STAT
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.String" resultMap="BaseResultMap">
    <!--@mbg.generated-->
    select 
    <include refid="Base_Column_List" />
    from cm_wechat_application
    where APPLICATION_CODE = #{applicationCode,jdbcType=VARCHAR}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.String">
    <!--@mbg.generated-->
    delete from cm_wechat_application
    where APPLICATION_CODE = #{applicationCode,jdbcType=VARCHAR}
  </delete>
  <insert id="insert" parameterType="com.howbuy.crm.wechat.dao.po.CmWechatApplicationPO">
    <!--@mbg.generated-->
    insert into cm_wechat_application (APPLICATION_CODE, APPLICATION_DESC, 
      COMPANY_NO, ACCESS_SECRET, AGENT_ID, 
      CREATOR, CREATE_TIME, MODIFIER, 
      MODIFY_TIME, REC_STAT)
    values (#{applicationCode,jdbcType=VARCHAR}, #{applicationDesc,jdbcType=VARCHAR}, 
      #{companyNo,jdbcType=VARCHAR}, #{accessSecret,jdbcType=VARCHAR}, #{agentId,jdbcType=VARCHAR}, 
      #{creator,jdbcType=VARCHAR}, #{createTime,jdbcType=TIMESTAMP}, #{modifier,jdbcType=VARCHAR}, 
      #{modifyTime,jdbcType=TIMESTAMP}, #{recStat,jdbcType=VARCHAR})
  </insert>
  <insert id="insertSelective" parameterType="com.howbuy.crm.wechat.dao.po.CmWechatApplicationPO">
    <!--@mbg.generated-->
    insert into cm_wechat_application
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="applicationCode != null">
        APPLICATION_CODE,
      </if>
      <if test="applicationDesc != null">
        APPLICATION_DESC,
      </if>
      <if test="companyNo != null">
        COMPANY_NO,
      </if>
      <if test="accessSecret != null">
        ACCESS_SECRET,
      </if>
      <if test="agentId != null">
        AGENT_ID,
      </if>
      <if test="creator != null">
        CREATOR,
      </if>
      <if test="createTime != null">
        CREATE_TIME,
      </if>
      <if test="modifier != null">
        MODIFIER,
      </if>
      <if test="modifyTime != null">
        MODIFY_TIME,
      </if>
      <if test="recStat != null">
        REC_STAT,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="applicationCode != null">
        #{applicationCode,jdbcType=VARCHAR},
      </if>
      <if test="applicationDesc != null">
        #{applicationDesc,jdbcType=VARCHAR},
      </if>
      <if test="companyNo != null">
        #{companyNo,jdbcType=VARCHAR},
      </if>
      <if test="accessSecret != null">
        #{accessSecret,jdbcType=VARCHAR},
      </if>
      <if test="agentId != null">
        #{agentId,jdbcType=VARCHAR},
      </if>
      <if test="creator != null">
        #{creator,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="modifier != null">
        #{modifier,jdbcType=VARCHAR},
      </if>
      <if test="modifyTime != null">
        #{modifyTime,jdbcType=TIMESTAMP},
      </if>
      <if test="recStat != null">
        #{recStat,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.howbuy.crm.wechat.dao.po.CmWechatApplicationPO">
    <!--@mbg.generated-->
    update cm_wechat_application
    <set>
      <if test="applicationDesc != null">
        APPLICATION_DESC = #{applicationDesc,jdbcType=VARCHAR},
      </if>
      <if test="companyNo != null">
        COMPANY_NO = #{companyNo,jdbcType=VARCHAR},
      </if>
      <if test="accessSecret != null">
        ACCESS_SECRET = #{accessSecret,jdbcType=VARCHAR},
      </if>
      <if test="agentId != null">
        AGENT_ID = #{agentId,jdbcType=VARCHAR},
      </if>
      <if test="creator != null">
        CREATOR = #{creator,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        CREATE_TIME = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="modifier != null">
        MODIFIER = #{modifier,jdbcType=VARCHAR},
      </if>
      <if test="modifyTime != null">
        MODIFY_TIME = #{modifyTime,jdbcType=TIMESTAMP},
      </if>
      <if test="recStat != null">
        REC_STAT = #{recStat,jdbcType=VARCHAR},
      </if>
    </set>
    where APPLICATION_CODE = #{applicationCode,jdbcType=VARCHAR}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.howbuy.crm.wechat.dao.po.CmWechatApplicationPO">
    <!--@mbg.generated-->
    update cm_wechat_application
    set APPLICATION_DESC = #{applicationDesc,jdbcType=VARCHAR},
      COMPANY_NO = #{companyNo,jdbcType=VARCHAR},
      ACCESS_SECRET = #{accessSecret,jdbcType=VARCHAR},
      AGENT_ID = #{agentId,jdbcType=VARCHAR},
      CREATOR = #{creator,jdbcType=VARCHAR},
      CREATE_TIME = #{createTime,jdbcType=TIMESTAMP},
      MODIFIER = #{modifier,jdbcType=VARCHAR},
      MODIFY_TIME = #{modifyTime,jdbcType=TIMESTAMP},
      REC_STAT = #{recStat,jdbcType=VARCHAR}
    where APPLICATION_CODE = #{applicationCode,jdbcType=VARCHAR}
  </update>



  <select id="selectConfigByApplicationCode" parameterType="string" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from cm_wechat_application
    where APPLICATION_CODE = #{applicationCode,jdbcType=VARCHAR}
    and REC_STAT = '1'
  </select>

  <select id="selectApplicationConfigList" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from cm_wechat_application
    where REC_STAT = '1'
  </select>
</mapper>