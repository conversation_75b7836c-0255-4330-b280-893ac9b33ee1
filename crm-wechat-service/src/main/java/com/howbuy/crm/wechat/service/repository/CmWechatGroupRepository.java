/**
 * Copyright (c) 2023, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.crm.wechat.service.repository;

import com.github.pagehelper.page.PageMethod;
import com.howbuy.crm.wechat.dao.mapper.CmWechatGroupMapper;
import com.howbuy.crm.wechat.dao.po.CmWechatGroupPO;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * @description: 企微客户群ID表
 * <AUTHOR>
 * @date 2023/10/25 17:03
 * @since JDK 1.8
 */

@Repository
@Transactional(propagation = Propagation.SUPPORTS, rollbackFor = Exception.class)
public class CmWechatGroupRepository {

    @Autowired
    private CmWechatGroupMapper cmWechatGroupMapper;


    /**
     * @description: 根据客户群id查询客户群信息
     * @param chatId 客户群id
     * @return com.howbuy.crm.wechat.dao.po.CmWechatGroupPo 客户群信息
     * @throws
     * @since JDK 1.8
     */
    public CmWechatGroupPO getByChatId(String chatId) {
        return cmWechatGroupMapper.getByChatId(chatId);
    }

    /**
     * @description: 更新客户群信息
     * @param cmWechatGroupPo 客户群信息
     * @return int 更新条数
     * @since JDK 1.8
     */
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = Exception.class)
    public int updateByChatId(CmWechatGroupPO cmWechatGroupPo) {
        return cmWechatGroupMapper.updateByChatId(cmWechatGroupPo);
    }

    /**
     * @description: 查询所有客户群id
     * @param companyNoList	企业编码列表
     * @return java.util.List<java.lang.String> 客户群id列表
     * @author: jin.wang03
     * @date: 2023/10/30 15:06
     * @since JDK 1.8
     */
    public List<CmWechatGroupPO> listByCompanyNo(List<String> companyNoList) {
        List<CmWechatGroupPO> resultList = new ArrayList<>();
        int page = 1;
        while (true) {
            PageMethod.startPage(page, 1000);
            List<CmWechatGroupPO> chatIdList = cmWechatGroupMapper.listByCompanyNoList(companyNoList);
            if (CollectionUtils.isEmpty(chatIdList)) {
                break;
            }
            resultList.addAll(chatIdList);

            page++;
        }
        return resultList;
    }


    /**
     * @description: 根据companyNo和员工id集合 查询所有客户群id
     * @param companyNo	企业编码
     * @return java.util.List<java.lang.String> 客户群id列表
     * @author: jin.wang03
     * @date: 2023/10/30 15:06
     * @since JDK 1.8
     */
    public List<String> listChatIdByCompanyNoAndUserIdList(String companyNo, List<String> userIdList) {
        return cmWechatGroupMapper.listChatIdByCompanyNoAndUserIdList(companyNo, userIdList);
    }

    /**
     * @param chatId 客户群id
     * @param date
     * @throws
     * @description: 根据客户群id删除客户群信息
     * @since JDK 1.8
     */
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = Exception.class)
    public int deleteByChatId(String chatId) {
        Date date= new Date();
        return cmWechatGroupMapper.deleteByChatId(chatId,date);
    }

    /**
     * @description: 批量删除客户群信息
     * @param bathDeleteChatIdList 客户群id列表
     * @throws
     * @since JDK 1.8
     */
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = Exception.class)
    public void batchDeleteByChatId(List<String> bathDeleteChatIdList) {
        cmWechatGroupMapper.batchDeleteByChatId(bathDeleteChatIdList);
    }

    /**
     * @description: 批量插入客户群信息
     * @param batchInsertList 客户群信息列表
     * @throws
     * @since JDK 1.8
     */
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = Exception.class)
    public int batchInsert(List<CmWechatGroupPO> batchInsertList) {
        return cmWechatGroupMapper.batchInsert(batchInsertList);
    }

    /**
     * @description: 根据客户群id列表查询客户群信息
     * @param chatIdList 客户群id列表
     * @return java.util.List<com.howbuy.crm.wechat.dao.po.CmWechatGroupPo> 客户群信息列表
     * @author: jin.wang03
     * @date: 2023/10/31 17:38
     * @since JDK 1.8
     */
    public List<CmWechatGroupPO> listByChatId(List<String> chatIdList) {
        return cmWechatGroupMapper.listByChatIdList(chatIdList);
    }

    /**
     * @description:(根据员工账号查询所有有他的群)
     * @param userId
     * @return java.util.List<com.howbuy.crm.wechat.dao.po.CmWechatGroupPO>
     * @author: shuai.zhang
     * @date: 2024/3/19 17:58
     * @since JDK 1.8
     */
    public List<CmWechatGroupPO> selectContainUserChat(String userId) {
        //Crocodile's TODO :  externalUserId  本身 companyNo 就不一样。 是否需要显示 参数
        return cmWechatGroupMapper.selectContainUserChat(userId);
    }
}