/*
 Navicat Premium Dump SQL

 Source Server         : CBH-************-3306-MySQL
 Source Server Type    : MySQL
 Source Server Version : 50729 (5.7.29-log)
 Source Host           : ************:3306
 Source Schema         : crm_wechat

 Target Server Type    : MySQL
 Target Server Version : 50729 (5.7.29-log)
 File Encoding         : 65001

 Date: 16/07/2025 13:20:52
*/

SET NAMES utf8mb4;
SET FOREIGN_KEY_CHECKS = 0;

-- ----------------------------
-- Table structure for cm_wechat_group_user
-- ----------------------------
DROP TABLE IF EXISTS `cm_wechat_group_user`;
CREATE TABLE `cm_wechat_group_user`  (
  `CHAT_ID` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '客户群id',
  `EXTERNAL_USER_ID` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '客户ID',
  `TYPE` varchar(10) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '客户类型 1-企业成员 2 - 外部联系人',
  `UNIONID` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '外部联系人在微信开放平台的唯一身份标识（微信unionid）',
  `JOIN_SCENE` varchar(10) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '入群方式。1 - 由成员邀请入群（直接邀请入群） 2 - 由成员邀请入群（通过邀请链接入群）3 - 通过扫描群二维码入群',
  `CREATE_TIME` datetime(6) NULL DEFAULT NULL COMMENT '创建时间',
  `JOIN_TIME` datetime(6) NULL DEFAULT NULL COMMENT '入群时间',
  `GROUP_NICKNAME` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '在群里的昵称',
  `NAME` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '如果是微信用户，则返回其在微信中设置的名字，如果是企业微信联系人，则返回其设置对外展示的别名或实名',
  `USERCHATFLAG` char(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '群成员状态 0在群 1退群',
  `company_no` varchar(2) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '企业编码 1-好买财富；2-好买基金',
  `invitor` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '邀请者',
  `update_time` datetime(6) NULL DEFAULT NULL COMMENT '更新时间',
  `leave_time` datetime(6) NULL DEFAULT NULL COMMENT '退群时间',
  PRIMARY KEY (`CHAT_ID`, `EXTERNAL_USER_ID`) USING BTREE,
  INDEX `idx_EXTERNAL_USER_ID`(`EXTERNAL_USER_ID`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci ROW_FORMAT = DYNAMIC;

SET FOREIGN_KEY_CHECKS = 1;
