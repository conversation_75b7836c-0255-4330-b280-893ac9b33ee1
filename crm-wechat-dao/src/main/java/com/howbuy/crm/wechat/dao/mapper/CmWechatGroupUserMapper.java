/**
 * Copyright (c) 2023, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.crm.wechat.dao.mapper;

import com.howbuy.crm.wechat.dao.bo.WechatGroupBO;
import com.howbuy.crm.wechat.dao.po.CmWechatGroupUserNewPO;
import com.howbuy.crm.wechat.dao.po.CmWechatGroupUserPO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.Date;
import java.util.List;

/**
 * @description: 企微客户群成员表Mapper接口类
 * <AUTHOR>
 * @date 2023/10/26 14:08
 * @since JDK 1.8
 */

@Mapper
public interface CmWechatGroupUserMapper {

    /**
     * @param chatId       客户群id
     * @param userChatFlag 群成员状态 0在群 1退群
     * @return java.util.List<com.howbuy.crm.wechat.dao.po.CmWechatGroupUserPo> 客户群成员列表
     * @description: 根据客户群id查询客户群成员列表
     * @author: jin.wang03
     * @date: 2023/10/26 15:34
     * @since JDK 1.8
     */
    List<CmWechatGroupUserPO> listByChatId(@Param("chatId") String chatId, @Param("userChatFlag") String userChatFlag);

    /**
     * @description: 批量插入
     * @param bathInsertList	批量插入的数据
     * @author: jin.wang03
     * @date: 2023/10/26 15:35
     * @since JDK 1.8
     */
    void batchInsert(@Param("batchInsertList") List<CmWechatGroupUserPO> bathInsertList);

    /**
     * @description: 根据客户群id和用户id更新数据
     * @param cmWechatGroupUserPo 更新数据
     * @author: jin.wang03
     * @date: 2023/10/26 15:37
     * @since JDK 1.8
     */
    void updateByChatIdAndUserId(CmWechatGroupUserPO cmWechatGroupUserPo);

    /**
     * @description: 批量逻辑删除
     * @param chatId 客户群ID
     * @param batchDeleteList 删除类别
     * @author: jin.wang03
     * @date: 2023/10/27 11:24
     * @since JDK 1.8
     */
    void batchDelete(@Param("chatId") String chatId, @Param("batchDeleteList") List<String> batchDeleteList, @Param("leaveTime")Date leaveTime);


    /**
     * @param chatId 客户群ID
     * @param date
     * @description: 根据客户群id逻辑删除数据，如果leave_time字段为空，则设置成date
     * @author: jin.wang03
     * @date: 2023/10/27 11:24
     * @since JDK 1.8
     */
    int deleteByChatId(@Param("chatId") String chatId,@Param("date")  Date date);

    /**
     * @description 查询客户当前所在群信息
     * @param externalUserId
     * @return
     * <AUTHOR>
     * @date 2024/5/27 4:34 PM
     * @since JDK 1.8
     */
    List<CmWechatGroupUserNewPO> selectNormalByUserId(String externalUserId);

    /**
     * @description 查询客户所在群信息(包括在群 不在群)
     * @param externalUserId
     * @return
     * <AUTHOR>
     * @date 2024/6/4 2:29 PM
     * @since JDK 1.8
     */
    List<CmWechatGroupUserNewPO> selectInfoByUserId(String externalUserId);

    /**
     * @description 根据chatid和userid查询信息
     * @param chatid
     * @param userid
     * @return
     * <AUTHOR>
     * @date 2024/5/28 10:34 AM
     * @since JDK 1.8
     */
    CmWechatGroupUserNewPO selectByChatIdAndUserId(@Param("chatid") String chatid, @Param("userid") String userid);
    
    /**
     * @description 查群状态
     * @param chatid	
     * @return 
     * <AUTHOR>
     * @date 2024/5/28 11:15 AM
     * @since JDK 1.8
     */
    WechatGroupBO selectGroupByChatId(@Param("chatid") String chatid);

    /**
     * @description:(有效的群成员)
     * @param chatId
     * @return java.util.List<com.howbuy.crm.wechat.dao.po.CmWechatGroupUserPO>
     * @author: shuai.zhang
     * @date: 2024/5/28 11:32
     * @since JDK 1.8
     */
    List<CmWechatGroupUserPO> listByChatIdEffective(@Param("chatId")String chatId);
}
