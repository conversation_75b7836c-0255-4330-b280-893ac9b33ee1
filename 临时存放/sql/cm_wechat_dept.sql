/*
 Navicat Premium Dump SQL

 Source Server         : CBH-************-3306-MySQL
 Source Server Type    : MySQL
 Source Server Version : 50729 (5.7.29-log)
 Source Host           : ************:3306
 Source Schema         : crm_wechat

 Target Server Type    : MySQL
 Target Server Version : 50729 (5.7.29-log)
 File Encoding         : 65001

 Date: 16/07/2025 13:19:53
*/

SET NAMES utf8mb4;
SET FOREIGN_KEY_CHECKS = 0;

-- ----------------------------
-- Table structure for cm_wechat_dept
-- ----------------------------
DROP TABLE IF EXISTS `cm_wechat_dept`;
CREATE TABLE `cm_wechat_dept`  (
  `ID` bigint(20) NOT NULL DEFAULT 0 COMMENT '主键',
  `DEPT_ID` int(11) NULL DEFAULT NULL COMMENT '部门id',
  `DEPT_NAME` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '部门名称',
  `PARENT_DEPT_ID` int(11) NULL DEFAULT NULL COMMENT '父级部门id，根部门id为1',
  `CREATE_TIME` datetime(6) NULL DEFAULT NULL COMMENT '创建时间',
  `UPDATE_TIME` datetime(6) NULL DEFAULT NULL COMMENT '修改时间',
  `DEL_FLAG` char(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '删除标志（0 正常；1 删除）',
  `DEL_TIME` datetime(6) NULL DEFAULT NULL COMMENT '删除时间',
  `COMPANY_NO` varchar(2) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '企业编码1好买财富2好买基金',
  PRIMARY KEY (`ID`) USING BTREE,
  INDEX `idx_dept_id`(`DEPT_ID`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '企业微信部门表' ROW_FORMAT = DYNAMIC;

SET FOREIGN_KEY_CHECKS = 1;
