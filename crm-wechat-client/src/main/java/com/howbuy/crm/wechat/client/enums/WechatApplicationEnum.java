/**
 * Copyright (c) 2024, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.crm.wechat.client.enums;

import java.util.ArrayList;
import java.util.List;

/**
 * @description: 企业微信-内部应用-枚举
 * NOTICE :  替换枚举： WechatAppEnum 和 WechatSecretEnum
 * <AUTHOR>
 * @date 2024/9/19 15:29
 * @since JDK 1.8
 */
public enum WechatApplicationEnum {

    /***********************************好买财富 ******************************************************/
    /**
     * [好买财富]企业微信-客户联系
     */
    WEALTH_CUSTOMER("wealth_customer", "客户联系", CompanyNoEnum.HOWBUY_WEALTH),

    /**
     * [好买财富]企业微信-自建应用-crm
     */
    WEALTH_CRM("wealth_crm", "自建应用-CRM", CompanyNoEnum.HOWBUY_WEALTH),

    /**
     * [好买财富]企业微信--商路通
     */
    WEALTH_RHSLT("wealth_rhslt", "商路通", CompanyNoEnum.HOWBUY_WEALTH),

    /**
     * [好买财富]企业微信-自建应用-画像
     */
    WEALTH_PORTRAIT("wealth_portrait", "自建应用-画像", CompanyNoEnum.HOWBUY_WEALTH),


    /***********************************好买财富 ******************************************************/





    /***********************************好买基金 ******************************************************/
    /**
     * [好买基金]企业微信-客户联系
     */
    FUND_CUSTOMER("fund_customer", "客户联系", CompanyNoEnum.HOWBUY_FUND),

    /***********************************好买基金 ******************************************************/




    /***********************************好晓买 ******************************************************/

    /**
     * [好晓买]企业微信-客户联系
     */
    HXM_CUSTOMER("hxm_customer", "客户联系", CompanyNoEnum.HOWBUY_HXM);
    /***********************************好晓买 ******************************************************/

    /**
     * 编码
     */
    private String code;


    /**
     * 描述
     */
    private String description;


    /**
     * 公司编号枚举
     */
    private CompanyNoEnum companyNoEnum;

    WechatApplicationEnum(String code,String description,CompanyNoEnum companyNoEnum) {
        this.code = code;
        this.description = description;
        this.companyNoEnum = companyNoEnum;
    }

    public String getCode() {
        return code;
    }
    public String getDescription() {
        return description;
    }
    public CompanyNoEnum getCompanyNoEnum() {
        return companyNoEnum;
    }



    /**
     * 根据公司编号获取 [企业微信-客户联系]  应用枚举
     * @param companyNoEnum
     * @return
     */
    public static WechatApplicationEnum getCustApplicationEnum(CompanyNoEnum companyNoEnum) {
        //TODO: 重构计划标记：
        // 企业自建应用【客户联系】，应该内置配在数据中
        WechatApplicationEnum applicationEnum =null;
        if(companyNoEnum==CompanyNoEnum.HOWBUY_WEALTH){
            applicationEnum = WechatApplicationEnum.WEALTH_CUSTOMER;
        }
        if(companyNoEnum==CompanyNoEnum.HOWBUY_FUND){
            applicationEnum = WechatApplicationEnum.FUND_CUSTOMER;
        }
        if(companyNoEnum==CompanyNoEnum.HOWBUY_HXM){
            applicationEnum = WechatApplicationEnum.HXM_CUSTOMER;
        }
        return applicationEnum;
    }

    /**
     * 根据编码获取枚举
     * @param code
     * @return
     */
    public static WechatApplicationEnum getEnum(String code) {
        for (WechatApplicationEnum value : WechatApplicationEnum.values()) {
            if (value.getCode().equals(code)) {
                return value;
            }
        }
        return null;
    }


    /**
     * 根据公司编号获取应用枚举列表
     * @param companyNoEnum
     * @return
     */
    public static  List<WechatApplicationEnum> getApplicationEnums(CompanyNoEnum companyNoEnum) {
        List<WechatApplicationEnum> list = new ArrayList<>();
        for (WechatApplicationEnum value : WechatApplicationEnum.values()) {
            if (value.getCompanyNoEnum().equals(companyNoEnum)) {
                list.add(value);
            }
        }
        return list;
    }



}
