/**
 * Copyright (c) 2024, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.crm.wechat.service.business.group;

import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import com.howbuy.common.utils.Assert;
import com.howbuy.crm.wechat.dao.po.CmWechatGroupPO;
import com.howbuy.crm.wechat.dao.po.CmWechatGroupUserPO;
import com.howbuy.crm.wechat.service.domain.externaluser.GroupChatInfo;
import com.howbuy.crm.wechat.service.domain.externaluser.GroupChatMemberInfo;
import com.howbuy.crm.wechat.service.outerservice.wechatapi.WechatExternalContactOuterService;
import com.howbuy.crm.wechat.service.repository.CmWechatGroupRepository;
import com.howbuy.crm.wechat.service.repository.CmWechatGroupUserRepository;
import crm.howbuy.base.enums.YesOrNoEnum;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

/**
 * @description: (企微群buss层)
 * <AUTHOR>
 * @date 2024/3/15 15:30
 * @since JDK 1.8
 */
@Slf4j
@Component
@Transactional
public class WechatGroupBusiness {
    @Autowired
    private CmWechatGroupRepository cmWechatGroupRepository;
    @Autowired
    private CmWechatGroupUserRepository cmWechatGroupUserRepository;
    @Autowired
    private WechatExternalContactOuterService wechatExternalContactOuterService;
    /**
     * @description:(根据员工账号查询所有有他的群)
     * @param userId
     * @return java.util.List<com.howbuy.crm.wechat.service.domain.externaluser.GroupChatInfo>
     * @author: shuai.zhang
     * @date: 2024/3/15 16:25
     * @since JDK 1.8
     */
    public List<GroupChatInfo> getGroupInfoByUserId(String userId) {
        ArrayList<GroupChatInfo> objects = Lists.newArrayList();
        //先查成员有该userid 且 群有效的群ID
        List<CmWechatGroupPO> chats = cmWechatGroupRepository.selectContainUserChat(userId);
        //循环所有群ID，查询群信息
        for (CmWechatGroupPO chat : chats) {
            // 给好臻机器人用的，只查询在群的
            List<CmWechatGroupUserPO> externalGroupInfoDTO = cmWechatGroupUserRepository.listByChatId(chat.getChatId(), YesOrNoEnum.NO.getCode());
            GroupChatInfo a = transToGroupChatInfo(chat,externalGroupInfoDTO);
            objects.add(a);
        }
        return objects;
    }

    /**
     * @description:(转换群信息)
     * @param chat	
     * @param externalGroupInfoDTO
     * @return com.howbuy.crm.wechat.service.domain.externaluser.GroupChatInfo
     * @author: shuai.zhang
     * @date: 2024/4/3 16:33
     * @since JDK 1.8
     */
    private GroupChatInfo transToGroupChatInfo(CmWechatGroupPO chat, List<CmWechatGroupUserPO> externalGroupInfoDTO) {
        GroupChatInfo groupChatInfo = new GroupChatInfo();
        groupChatInfo.setChatId(chat.getChatId());
        groupChatInfo.setChatName(chat.getChatName());
        groupChatInfo.setChatOwner(chat.getChatOwner());
        groupChatInfo.setCreateTime(chat.getCreateTime());
        groupChatInfo.setChatMemberList( Lists.newArrayList());
        if(CollectionUtils.isNotEmpty(externalGroupInfoDTO)){
            List<GroupChatMemberInfo> groupChatMemberInfos = externalGroupInfoDTO.stream().map(member -> {
                return GroupChatMemberInfo.builder()
                        .userId(member.getExternalUserId())
                        .type(member.getType())
                        .joinTime(member.getJoinTime())
                        .joinScene(member.getJoinScene())
                        .invitorUserId(member.getInvitor())
                        .groupNickname(member.getGroupNickName())
                        .name(member.getName())
                        .unionid(member.getUnionId())
                        .build();
            }).collect(Collectors.toList());
            groupChatInfo.setChatMemberList(groupChatMemberInfos);
        }
        return groupChatInfo;
    }

    /**
     * @description:(根据群ID获取群信息)
     * @param chatId
     * @return com.howbuy.crm.wechat.service.domain.externaluser.GroupChatInfo
     * @author: shuai.zhang
     * @date: 2024/4/2 13:47
     * @since JDK 1.8
     */
    public GroupChatInfo getGroupInfoByChatId(String chatId) {
        Assert.notNull(chatId, "群ID不能为空！");
        CmWechatGroupPO chat = cmWechatGroupRepository.getByChatId(chatId);
        List<CmWechatGroupUserPO> externalGroupInfoDTO = cmWechatGroupUserRepository.listByChatIdEffective(chatId);
        GroupChatInfo a = transToGroupChatInfo(chat,externalGroupInfoDTO);
        log.info("根据chatId:{},获取群信息:{}",chatId, JSON.toJSON(a));
        return a;
    }
}