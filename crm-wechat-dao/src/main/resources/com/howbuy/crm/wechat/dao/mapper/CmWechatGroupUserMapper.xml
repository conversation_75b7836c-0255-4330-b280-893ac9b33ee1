<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.howbuy.crm.wechat.dao.mapper.CmWechatGroupUserMapper">
    <sql id="Base_Column_List">
        <!--@mbg.generated-->
        `CHAT_ID`,  `EXTERNAL_USER_ID`, `TYPE`, `UNIONID`, `JOIN_SCENE`,
        `CREATE_TIME`, `JOIN_TIME`, `GROUP_NICKNAME`, `NAME`, `<PERSON><PERSON>CH<PERSON><PERSON>AG`,
        `UPDATE_TIME`, `COMPANY_NO`, `INVITOR`
    </sql>

    <select id="listByChatId" resultType="com.howbuy.crm.wechat.dao.po.CmWechatGroupUserPO">
        select <include refid="Base_Column_List"/>
        from cm_wechat_group_user
        where chat_id = #{chatId,jdbcType=VARCHAR}
        <if test="userChatFlag != null and userChatFlag != ''">
            and userChatFlag = #{userChatFlag,jdbcType=VARCHAR}
        </if>

    </select>

    <insert id="batchInsert">
        insert into cm_wechat_group_user (<include refid="Base_Column_List"/>)
        values
        <foreach collection="batchInsertList" item="item" index="index" separator=",">
            (#{item.chatId}, #{item.externalUserId}, #{item.type}, #{item.unionId}, #{item.joinScene},
            #{item.createTime}, #{item.joinTime}, #{item.groupNickName}, #{item.name}, #{item.userChatFlag},
            #{item.updateTime}, #{item.companyNo}, #{item.invitor})
        </foreach>
    </insert>

    <update id="updateByChatIdAndUserId">
        update cm_wechat_group_user
        <set>
            <if test="type != null and type != ''">
                type = #{type},
            </if>
            <if test="unionId != null and unionId != ''">
                unionid = #{unionId},
            </if>
            <if test="joinScene != null and joinScene != ''">
                join_scene = #{joinScene},
            </if>
            <if test="joinTime != null">
                join_time = #{joinTime},
            </if>
            <if test="groupNickName != null and groupNickName != ''">
                group_nickname = #{groupNickName},
            </if>
            <if test="name != null and name != ''">
                name = #{name},
            </if>
            <if test="userChatFlag != null and userChatFlag != ''">
                userchatflag = #{userChatFlag},
            </if>
            <if test="updateTime != null">
                update_time = #{updateTime},
            </if>
            <if test="companyNo != null and companyNo != ''">
                company_no = #{companyNo},
            </if>
            <if test="invitor != null and invitor != ''">
                invitor = #{invitor},
            </if>
        </set>
        where chat_id = #{chatId} and external_user_id = #{externalUserId}
    </update>

    <update id="batchDelete">
        update cm_wechat_group_user set userChatFlag = '1'
        <if test="leaveTime != null">
            , leave_time = #{leaveTime,jdbcType=TIMESTAMP}
        </if>
        where chat_id = #{chatId} and external_user_id in
        <foreach collection="batchDeleteList" item="externalUserId" open="(" separator="," close=")">
            #{externalUserId}
        </foreach>
        and userChatFlag = '0'
    </update>

    <update id="deleteByChatId">
        update cm_wechat_group_user
        set userChatFlag = '1',
        UPDATE_TIME=#{date,jdbcType=TIMESTAMP},
        LEAVE_TIME = CASE WHEN LEAVE_TIME IS NULL THEN #{date,jdbcType=TIMESTAMP} ELSE LEAVE_TIME END
        where chat_id = #{chatId}
    </update>

    <resultMap id="BaseResultMap1" type="com.howbuy.crm.wechat.dao.po.CmWechatGroupUserNewPO">
        <id column="CHAT_ID" jdbcType="VARCHAR" property="chatid" />
        <id column="EXTERNAL_USER_ID" jdbcType="VARCHAR" property="userid" />
        <result column="CHAT_NAME" jdbcType="VARCHAR" property="chatname" />
        <result column="CHAT_OWNER" jdbcType="VARCHAR" property="chatowner" />
        <result column="TYPE" jdbcType="VARCHAR" property="type" />
        <result column="UNIONID" jdbcType="VARCHAR" property="unionid" />
        <result column="JOIN_SCENE" jdbcType="VARCHAR" property="joinScene" />
        <result column="DEPT_ID" jdbcType="VARCHAR" property="departmentid" />
        <result column="DEPT_NAME" jdbcType="VARCHAR" property="departmentname" />
        <result column="CREATE_TIME" jdbcType="TIMESTAMP" property="createdt" />
        <result column="JOIN_TIME" jdbcType="TIMESTAMP" property="joinTime" />
        <result column="CHAT_FLAG" jdbcType="VARCHAR" property="chatflag" />
        <result column="GROUP_NICKNAME" jdbcType="VARCHAR" property="groupNickname" />
        <result column="NAME" jdbcType="VARCHAR" property="name" />
        <result column="USERCHATFLAG" jdbcType="VARCHAR" property="userchatflag" />
        <result column="LEAVE_TIME" jdbcType="TIMESTAMP" property="leaveTime" />
    </resultMap>

    <select id="selectNormalByUserId" resultMap="BaseResultMap1">
        select
        a.CHAT_ID, b.EXTERNAL_USER_ID, a.CHAT_NAME, a.CHAT_OWNER, b.TYPE, b.UNIONID, b.JOIN_SCENE, a.DEPT_ID, a.DEPT_NAME,
        b.CREATE_TIME, b.JOIN_TIME, a.CHAT_FLAG, b.GROUP_NICKNAME, b.NAME, b.USERCHATFLAG, b.leave_time
        <!--Crocodile's TODO : 是否需要显式指定 companyNo ? -->
        from  cm_wechat_group a
        left join CM_WECHAT_GROUP_USER b on a.CHAT_ID = b.CHAT_ID
        where b.EXTERNAL_USER_ID = #{userId,jdbcType=VARCHAR}
        and a.CHAT_FLAG = '0' AND USERCHATFLAG = '0'
    </select>

    <select id="selectInfoByUserId" resultMap="BaseResultMap1">
        select
        a.CHAT_ID, b.EXTERNAL_USER_ID, a.CHAT_NAME, a.CHAT_OWNER, b.TYPE, b.UNIONID, b.JOIN_SCENE, a.DEPT_ID, a.DEPT_NAME,
        b.CREATE_TIME, b.JOIN_TIME, a.CHAT_FLAG, b.GROUP_NICKNAME, b.NAME, b.USERCHATFLAG, b.leave_time
        <!--Crocodile's TODO : 是否需要显式指定 companyNo ? -->
        from  cm_wechat_group a
        left join CM_WECHAT_GROUP_USER b on a.CHAT_ID = b.CHAT_ID
        where b.EXTERNAL_USER_ID = #{userId,jdbcType=VARCHAR}

    </select>

    <select id="selectByChatIdAndUserId" resultMap="BaseResultMap1">
        select
        *
        from CM_WECHAT_GROUP_USER
        where CHAT_ID = #{chatid,jdbcType=VARCHAR}
        and EXTERNAL_USER_ID = #{userid,jdbcType=VARCHAR}
    </select>

    <resultMap id="wechatgroupbo" type="com.howbuy.crm.wechat.dao.bo.WechatGroupBO">
        <id column="CHAT_ID" jdbcType="VARCHAR" property="chatId" />
        <result column="CHAT_FLAG" jdbcType="VARCHAR" property="chatFlag" />
        <result column="COUNT" jdbcType="INTEGER" property="count" />
    </resultMap>

    <select id="selectGroupByChatId" resultMap="wechatgroupbo">
        select a.CHAT_FLAG
        from cm_wechat_group a
        where a.CHAT_ID = #{chatid,jdbcType=VARCHAR}
    </select>

    <select id="listByChatIdEffective" resultType="com.howbuy.crm.wechat.dao.po.CmWechatGroupUserPO">
        select
        <include refid="Base_Column_List"/>
        from cm_wechat_group_user
        where chat_id = #{chatId}
        and  USERCHATFLAG ='0'
    </select>
</mapper>