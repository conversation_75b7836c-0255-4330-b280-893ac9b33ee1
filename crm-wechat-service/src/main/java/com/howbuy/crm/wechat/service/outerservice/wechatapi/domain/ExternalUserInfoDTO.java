package com.howbuy.crm.wechat.service.outerservice.wechatapi.domain;

import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @description: 企业微信成员信息DTO
 * @date 2025-06-18 16:06:07
 * @since JDK 1.8
 */
@Data
public class ExternalUserInfoDTO extends ExternalWeChatBaseDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 成员UserID。对应管理端的账号
     */
    private String userid;

    /**
     * 成员名称；第三方不可获取，调用时返回userid以代替name；代开发自建应用需要管理员授权才返回；对于非第三方创建的成员，第三方通讯录应用也不可获取；未返回名称的情况需要通过通讯录展示组件来展示名字
     */
    private String name;

    /**
     * 成员所属部门id列表，仅返回该应用有查看权限的部门id。对授权了“组织架构信息”的第三方应用或授权了“组织架构信息”-“部门及父部门ID、部门负责人”的代开发应用，返回成员所属的全部部门id列表
     */
    private List<Integer> department;

    /**
     * 部门内的排序值，默认为0。数量必须和department一致，数值越大排序越前面。值范围是[0, 2^32)
     */
    private List<Integer> order;

    /**
     * 职务信息；代开发自建应用需要管理员授权才返回；第三方仅通讯录应用可获取；对于非第三方创建的成员，第三方通讯录应用也不可获取；上游企业不可获取下游企业成员该字段
     */
    private String position;

    /**
     * 手机号码，代开发自建应用需要管理员授权才返回；第三方仅通讯录应用可获取；对于非第三方创建的成员，第三方通讯录应用也不可获取；上游企业不可获取下游企业成员该字段
     */
    private String mobile;

    /**
     * 性别。0表示未定义，1表示男性，2表示女性。第三方仅通讯录应用可获取；对于非第三方创建的成员，第三方通讯录应用也不可获取；上游企业不可获取下游企业成员该字段。注：不可获取指返回值为0
     */
    private String gender;

    /**
     * 邮箱，代开发自建应用需要管理员授权才返回；第三方仅通讯录应用可获取；对于非第三方创建的成员，第三方通讯录应用也不可获取；上游企业不可获取下游企业成员该字段
     */
    private String email;

    /**
     * 企业邮箱，代开发自建应用不返回；第三方仅通讯录应用可获取；对于非第三方创建的成员，第三方通讯录应用也不可获取；上游企业不可获取下游企业成员该字段
     */
    private String bizMail;

    /**
     * 表示在所在的部门内是否为部门负责人。0-否；1-是。是一个列表，
     * 数量必须与department一致。
     * 第三方通讯录应用或者授权了“组织架构信息-应用可获取企业的部门组织架构信息-部门负责人”权限的第三方应用和代开发应用可获取；
     * 对于非第三方创建的成员，第三方通讯录应用不可获取；上游企业不可获取下游企业成员该字段
     */
    private List<Integer> isLeaderInDept;

    /**
     * 直属上级UserID，返回在应用可见范围内的直属上级列表，最多有1个直属上级；
     * 第三方通讯录应用或者授权了“组织架构信息-应用可获取可见范围内成员组织架构信息-直属上级”权限的第三方应用和代开发应用可获取；
     * 对于非第三方创建的成员，第三方通讯录应用不可获取；上游企业不可获取下游企业成员该字段；代开发自建应用不可获取该字段
     */
    private List<String> directLeader;

    /**
     * 头像url。 第三方仅通讯录应用可获取；对于非第三方创建的成员，第三方通讯录应用也不可获取；上游企业不可获取下游企业成员该字段
     */
    private String avatar;

    /**
     * 头像缩略图url。第三方仅通讯录应用可获取；对于非第三方创建的成员，第三方通讯录应用也不可获取；上游企业不可获取下游企业成员该字段
     */
    private String thumbAvatar;

    /**
     * 座机。代开发自建应用需要管理员授权才返回；第三方仅通讯录应用可获取；对于非第三方创建的成员，第三方通讯录应用也不可获取；上游企业不可获取下游企业成员该字段
     */
    private String telephone;

    /**
     * 别名；第三方仅通讯录应用可获取；对于非第三方创建的成员，第三方通讯录应用也不可获取；上游企业不可获取下游企业成员该字段
     */
    private String alias;

    /**
     * 地址。代开发自建应用需要管理员授权才返回；第三方仅通讯录应用可获取；对于非第三方创建的成员，第三方通讯录应用也不可获取；上游企业不可获取下游企业成员该字段
     */
    private String address;

    /**
     * 全局唯一。对于同一个服务商，不同应用获取到企业内同一个成员的open_userid是相同的，最多64个字节。仅第三方应用可获取
     */
    private String openUserid;

    /**
     * 主部门，仅当应用对主部门有查看权限时返回。
     */
    private Integer mainDepartment;

    /**
     * 扩展属性，字段详见成员扩展属性。代开发自建应用需要管理员授权才返回；第三方仅通讯录应用可获取；对于非第三方创建的成员，第三方通讯录应用也不可获取；上游企业不可获取下游企业成员该字段
     */
    private ExtAttr extattr;

    /**
     * 激活状态: 1=已激活，2=已禁用，4=未激活，5=退出企业。
     * 已激活代表已激活企业微信或已关注微信插件（原企业号）。未激活代表既未激活企业微信又未关注微信插件（原企业号）。
     */
    private Integer status;

    /**
     * 员工个人二维码，扫描可添加为外部联系人(注意返回的是一个url，可在浏览器上打开该url以展示二维码)；第三方仅通讯录应用可获取；对于非第三方创建的成员，第三方通讯录应用也不可获取；上游企业不可获取下游企业成员该字段
     */
    private String qrCode;

    /**
     * 对外职务，如果设置了该值，则以此作为对外展示的职务，否则以position来展示。代开发自建应用需要管理员授权才返回；第三方仅通讯录应用可获取；对于非第三方创建的成员，第三方通讯录应用也不可获取；上游企业不可获取下游企业成员该字段
     */
    private String externalPosition;

    /**
     * 成员对外属性，字段详情见对外属性；代开发自建应用需要管理员授权才返回；第三方仅通讯录应用可获取；对于非第三方创建的成员，第三方通讯录应用也不可获取；上游企业不可获取下游企业成员该字段
     */
    private ExternalProfile externalProfile;

    /**
     * 扩展属性
     */
    @Data
    public static class ExtAttr implements Serializable {
        private static final long serialVersionUID = 1L;
        
        /**
         * 属性列表
         */
        private List<Attr> attrs;
    }

    /**
     * 属性
     */
    @Data
    public static class Attr implements Serializable {
        private static final long serialVersionUID = 1L;
        
        /**
         * 属性类型
         */
        private Integer type;
        
        /**
         * 属性名称
         */
        private String name;
        
        /**
         * 文本属性
         */
        private TextAttr text;
        
        /**
         * 网页属性
         */
        private WebAttr web;
        
        /**
         * 小程序属性
         */
        private MiniProgramAttr miniprogram;
    }

    /**
     * 文本属性
     */
    @Data
    public static class TextAttr implements Serializable {
        private static final long serialVersionUID = 1L;
        
        /**
         * 文本值
         */
        private String value;
    }

    /**
     * 网页属性
     */
    @Data
    public static class WebAttr implements Serializable {
        private static final long serialVersionUID = 1L;
        
        /**
         * 网页URL
         */
        private String url;
        
        /**
         * 网页标题
         */
        private String title;
    }

    /**
     * 小程序属性
     */
    @Data
    public static class MiniProgramAttr implements Serializable {
        private static final long serialVersionUID = 1L;
        
        /**
         * 小程序appid
         */
        private String appid;
        
        /**
         * 小程序页面路径
         */
        private String pagepath;
        
        /**
         * 小程序标题
         */
        private String title;
    }

    /**
     * 成员对外属性
     */
    @Data
    public static class ExternalProfile implements Serializable {
        private static final long serialVersionUID = 1L;
        
        /**
         * 企业简称
         */
        private String externalCorpName;
        
        /**
         * 视频号
         */
        private WechatChannels wechatChannels;
        
        /**
         * 对外属性
         */
        private List<Attr> externalAttr;
    }

    /**
     * 视频号
     */
    @Data
    public static class WechatChannels implements Serializable {
        private static final long serialVersionUID = 1L;
        
        /**
         * 视频号名称
         */
        private String nickname;
        
        /**
         * 视频号状态
         */
        private Integer status;
    }
}