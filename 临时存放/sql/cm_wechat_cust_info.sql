/*
 Navicat Premium Dump SQL

 Source Server         : CBH-************-3306-MySQL
 Source Server Type    : MySQL
 Source Server Version : 50729 (5.7.29-log)
 Source Host           : ************:3306
 Source Schema         : crm_wechat

 Target Server Type    : MySQL
 Target Server Version : 50729 (5.7.29-log)
 File Encoding         : 65001

 Date: 16/07/2025 13:19:37
*/

SET NAMES utf8mb4;
SET FOREIGN_KEY_CHECKS = 0;

-- ----------------------------
-- Table structure for cm_wechat_cust_info
-- ----------------------------
DROP TABLE IF EXISTS `cm_wechat_cust_info`;
CREATE TABLE `cm_wechat_cust_info`  (
  `ID` bigint(20) NOT NULL DEFAULT 0 COMMENT 'id',
  `HBONE_NO` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '一账通号',
  `EXTERNAL_USER_ID` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '外部应用用户ID',
  `UNIONID` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '微信UnionId',
  `NICK_NAME` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '昵称',
  `CREATOR` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '创建人',
  `CREATE_TIME` datetime(6) NULL DEFAULT NULL COMMENT '创建时间',
  `MODIFIER` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '修改人',
  `UPDATE_TIME` datetime(6) NULL DEFAULT NULL COMMENT '修改时间',
  `COMPANY_NO` varchar(2) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '企业编码1好买财富2好买基金',
  `wechat_avatar` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '微信头像',
  PRIMARY KEY (`ID`) USING BTREE,
  UNIQUE INDEX `IDX_WECHAT_CUST_INFO_EXID`(`EXTERNAL_USER_ID`) USING BTREE,
  INDEX `idx_hbone_no`(`HBONE_NO`) USING BTREE,
  INDEX `idx_UNIONID`(`UNIONID`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '客户微信信息表' ROW_FORMAT = DYNAMIC;

SET FOREIGN_KEY_CHECKS = 1;
