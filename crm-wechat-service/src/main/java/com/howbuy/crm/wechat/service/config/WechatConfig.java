package com.howbuy.crm.wechat.service.config;

import com.howbuy.crm.wechat.service.commom.utils.UuidTaskDecorator;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.InitializingBean;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.core.task.TaskDecorator;
import org.springframework.stereotype.Component;

/**
 * @description: 企业微信nacos对应配置
 * NOTICE :  2025年7月11日 。该 config 不应该暴露给 业务代码 直接使用
 * @author: yu.zhang
 * @date: 2023/6/8 9:49
 * @version: 1.0
 * @since JDK 1.8
 */
@Slf4j
@Component
@Configuration
public class WechatConfig implements InitializingBean {
    /**
     * 好买财富回调Token，配置在好买财富企业微信
     */
    public static String wealthToken;


    /**
     * 好买财富回调Token，NACOS-配置
     */
    @Value("${wechat.wealth.token}")
    private String wealthTokenConfig;

    /**
     * 好买财富企业ID，好买财富企业微信固定ID
     * 每个企业都拥有唯一的corpid，获取此信息可在管理后台“我的企业”－“企业信息”下查看“企业ID”
     */
    public static String wealthCorpid;

    @Value("${wechat.wealth.corpid}")
    private String wealthCorpidConfig;
    /**
     * 好买财富企业回调自定义AESkey
     * EncodingAESKey用于消息内容加密
     */
    public static String wealthEncodingAESkey;

    @Value("${wechat.wealth.encodingaeskey}")
    private String wealthEncodingAESkeyConfig;
    /**
     * 好买财富企业微信-客户联系-客户secret
     */
    public static String wealthCorpSecret;

    @Value("${wechat.wealth.corpsecret.cust}")
    private String wealthCorpSecretConfig;
    /**
     * 好买财富 自建应用-crm-secret
     */
    public static String crmCorpSecret;

    @Value("${wechat.wealth.corpsecret.crm}")
    private String crmCorpSecretConfig;

    /**
     * 好买财富 自建应用-画像-secret
     */
    public static String wealthPortraitSecret;

    @Value("${wechat.wealth.portrait.secret}")
    private String wealthPortraitSecretConfig;

    /**
     * 好买财富 自建应用-画像-agentId
     */
    public static String wealthPortraitAgentId;

    @Value("${wechat.wealth.portrait.agentId}")
    private String wealthPortraitAgentIdConfig;

    /**
     * 自建应用-商路通-secret
     */
    public static String rhsltCorpSecret;

    @Value("${wechat.wealth.corpsecret.rhslt}")
    private String rhsltCorpSecretConfig;
    /**
     * 好买基金回调Token，配置在好买基金企业微信
     */
    public static String fundToken;

    @Value("${wechat.fund.token}")
    private String fundTokenConfig;
    /**
     * 好买基金企业ID，好买基金企业微信固定ID
     */
    public static String fundCorpid;

    @Value("${wechat.fund.corpid}")
    private String fundCorpidConfig;
    /**
     * 好买基金企业回调自定义AESkey
     * EncodingAESKey用于消息内容加密
     */
    public static String fundEncodingAESkey;

    @Value("${wechat.fund.encodingaeskey}")
    private String fundEncodingAESkeyConfig;
    /**
     * 好买财富企业微信-客户联系-客户secret
     */
    public static String fundCorpSecret;

    @Value("${wechat.fund.corpsecret.cust}")
    private String fundCorpSecretConfig;

    @Override
    public void afterPropertiesSet() {
        wealthToken = this.wealthTokenConfig;
        wealthCorpid = this.wealthCorpidConfig;
        wealthEncodingAESkey = this.wealthEncodingAESkeyConfig;
        wealthCorpSecret = this.wealthCorpSecretConfig;
        crmCorpSecret = this.crmCorpSecretConfig;
        rhsltCorpSecret = this.rhsltCorpSecretConfig;
        fundToken = this.fundTokenConfig;
        fundCorpid = this.fundCorpidConfig;
        fundEncodingAESkey = this.fundEncodingAESkeyConfig;
        fundCorpSecret = this.fundCorpSecretConfig;
        wealthPortraitSecret = this.wealthPortraitSecretConfig;
        wealthPortraitAgentId = this.wealthPortraitAgentIdConfig;
    }


    //    @Bean(name = "uuidTaskDecorator")
    @Bean(name = "uuidTaskDecorator")
    public TaskDecorator uuidTaskDecorator() {
        return new UuidTaskDecorator();
    }

}
