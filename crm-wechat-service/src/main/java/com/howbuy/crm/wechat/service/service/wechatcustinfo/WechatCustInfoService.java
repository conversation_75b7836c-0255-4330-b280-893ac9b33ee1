/**
 * Copyright (c) 2024, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.crm.wechat.service.service.wechatcustinfo;

import com.howbuy.crm.wechat.client.domain.request.wechatcustinfo.QueryWechatCustInfoRequest;
import com.howbuy.crm.wechat.client.domain.response.wechatcustinfo.WechatCustInfoVO;
import com.howbuy.crm.wechat.client.enums.CompanyNoEnum;
import com.howbuy.crm.wechat.client.enums.ResponseCodeEnum;
import com.howbuy.crm.wechat.dao.po.CmWechatCustInfoPO;
import com.howbuy.crm.wechat.service.commom.exception.BusinessException;
import com.howbuy.crm.wechat.service.repository.CmWechatCustInfoRepository;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Objects;

/**
 * <AUTHOR>
 * @description: 微信客户信息服务
 * @date 2024/9/6 11:28
 * @since JDK 1.8
 */
@Service
@Slf4j
public class WechatCustInfoService {

    @Resource
    private CmWechatCustInfoRepository cmWechatCustInfoRepository;

    /**
     * @param request
     * @return com.howbuy.crm.wechat.client.producer.wechatcustinfo.vo.WechatCustInfoVO
     * @description: 查询微信客户信息
     * <AUTHOR>
     * @date 2024/9/6 11:35
     * @since JDK 1.8
     */
    public WechatCustInfoVO queryWechatCustInfo(QueryWechatCustInfoRequest request) {
        if (StringUtils.isEmpty(request.getExternalUserId()) && StringUtils.isEmpty(request.getHboneNo())) {
            throw new BusinessException(ResponseCodeEnum.PARAM_ERROR);
        }
        CompanyNoEnum companyNoEnum=CompanyNoEnum.getEnum(request.getCompanyNo());
        if (companyNoEnum == null) {
            //入口 companyNo 默认赋值：
            companyNoEnum=CompanyNoEnum.HOWBUY_WEALTH;
        }

        CmWechatCustInfoPO cmWechatCustInfoPO;
        if (StringUtils.isNotEmpty(request.getExternalUserId())) {
            // 根据外部联系人id、好买财富COMPANY_NO查询微信客户信息
            cmWechatCustInfoPO = cmWechatCustInfoRepository.getWechatCustByExternalUserId(request.getExternalUserId(), companyNoEnum);
        } else {
            // 根据一账通号、好买财富COMPANY_NO获取微信客户信息
            cmWechatCustInfoPO = cmWechatCustInfoRepository.getExternalUserByHboneNo(request.getHboneNo(), companyNoEnum);
        }

        if (Objects.isNull(cmWechatCustInfoPO)) {
            return null;
        }

        WechatCustInfoVO wechatCustInfoVO = new WechatCustInfoVO();
        BeanUtils.copyProperties(cmWechatCustInfoPO, wechatCustInfoVO);
        return wechatCustInfoVO;
    }
}
