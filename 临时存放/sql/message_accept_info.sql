/*
 Navicat Premium Dump SQL

 Source Server         : CBH-************-3306-MySQL
 Source Server Type    : MySQL
 Source Server Version : 50729 (5.7.29-log)
 Source Host           : ************:3306
 Source Schema         : crm_wechat

 Target Server Type    : MySQL
 Target Server Version : 50729 (5.7.29-log)
 File Encoding         : 65001

 Date: 16/07/2025 13:21:37
*/

SET NAMES utf8mb4;
SET FOREIGN_KEY_CHECKS = 0;

-- ----------------------------
-- Table structure for message_accept_info
-- ----------------------------
DROP TABLE IF EXISTS `message_accept_info`;
CREATE TABLE `message_accept_info`  (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键id',
  `UNIQUE_ID` varchar(250) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '业务唯一标识',
  `message_type` varchar(3) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '消息类型：1-营销喜报',
  `MESSAGE_PARAMS` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '消息参数，JSON串',
  `message_status` varchar(2) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '消息状态：0-未处理；1-已处理；2-处理中',
  `create_time` datetime NOT NULL COMMENT '创建时间',
  `update_time` datetime NULL DEFAULT NULL COMMENT '更新时间',
  `TEMPLATE_ID` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '消息模版id',
  `TEMPLATE_PARAMS` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '消息模版id 消息参数',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE INDEX `uk_message_accept_info`(`message_type`, `UNIQUE_ID`) USING BTREE,
  INDEX `idx_message_status`(`message_status`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 160857 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '消息接收表' ROW_FORMAT = Dynamic;

SET FOREIGN_KEY_CHECKS = 1;
