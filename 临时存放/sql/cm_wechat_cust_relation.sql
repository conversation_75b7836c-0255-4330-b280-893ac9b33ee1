/*
 Navicat Premium Dump SQL

 Source Server         : CBH-************-3306-MySQL
 Source Server Type    : MySQL
 Source Server Version : 50729 (5.7.29-log)
 Source Host           : ************:3306
 Source Schema         : crm_wechat

 Target Server Type    : MySQL
 Target Server Version : 50729 (5.7.29-log)
 File Encoding         : 65001

 Date: 16/07/2025 13:19:46
*/

SET NAMES utf8mb4;
SET FOREIGN_KEY_CHECKS = 0;

-- ----------------------------
-- Table structure for cm_wechat_cust_relation
-- ----------------------------
DROP TABLE IF EXISTS `cm_wechat_cust_relation`;
CREATE TABLE `cm_wechat_cust_relation`  (
  `ID` bigint(20) NOT NULL DEFAULT 0 COMMENT 'id',
  `CONSCODE` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '投顾号',
  `EXTERNAL_USER_ID` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '外部应用用户ID',
  `STATUS` varchar(2) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '状态1新增2删除客户3被客户删除',
  `ADD_TIME` datetime(6) NULL DEFAULT NULL COMMENT '添加时间',
  `DEL_TIME` datetime(6) NULL DEFAULT NULL COMMENT '删除时间',
  `CREATOR` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '创建人',
  `CREATE_TIME` datetime(6) NULL DEFAULT NULL COMMENT '创建时间',
  `MODIFIER` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '修改人',
  `UPDATE_TIME` datetime(6) NULL DEFAULT NULL COMMENT '修改时间',
  `ADD_WAY` varchar(10) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '添加客户的来源',
  `STATE` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '添加客户的渠道',
  `COMPANY_NO` varchar(2) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '企业编码1好买财富2好买基金',
  `first_add_time` datetime(6) NULL DEFAULT NULL COMMENT '首次添加时间',
  PRIMARY KEY (`ID`) USING BTREE,
  UNIQUE INDEX `IDX_WECHATRELATION_EXIDCONS`(`CONSCODE`, `EXTERNAL_USER_ID`) USING BTREE,
  INDEX `IDX_WECHAT_CUST_RELATION_EXID`(`EXTERNAL_USER_ID`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '客户微信投顾关联关系表' ROW_FORMAT = DYNAMIC;

SET FOREIGN_KEY_CHECKS = 1;
