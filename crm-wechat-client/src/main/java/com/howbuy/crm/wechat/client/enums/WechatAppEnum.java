/**
 * Copyright (c) 2024, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.crm.wechat.client.enums;

/**
 * @description: 微信应用枚举
 * <AUTHOR>
 * @date 2024/9/19 15:29
 * @since JDK 1.8
 */
@Deprecated
public enum WechatAppEnum {

    /**
     * 同 {@link com.howbuy.crm.wechat.client.enums.WechatApplicationEnum}
     */

    WEALTH_CUSTOMER("wealth_customer"),

    WEALTH_CRM("wealth_crm"),

    WEALTH_RHSLT("wealth_rhslt"),

    WEALTH_PORTRAIT("wealth_portrait"),

    FUND_CUSTOMER("fund_customer"),

    ;

    private String key;

    WechatAppEnum(String key) {
        this.key = key;
    }

    public String getKey() {
        return key;
    }

}
