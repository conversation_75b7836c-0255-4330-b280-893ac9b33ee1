/**
 * Copyright (c) 2025, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.crm.wechat.service.commom.utils;

import com.howbuy.crm.wechat.client.enums.CompanyNoEnum;
import com.howbuy.crm.wechat.client.enums.ResponseCodeEnum;
import com.howbuy.crm.wechat.client.enums.WechatApplicationEnum;
import com.howbuy.crm.wechat.service.commom.aes.WXBizMsgCrypt;
import com.howbuy.crm.wechat.service.commom.exception.BusinessException;
import com.howbuy.crm.wechat.service.config.WechatConfig;
import lombok.extern.slf4j.Slf4j;

/**
 * @description: (企业微信-企业主体-映射关系)
 * <AUTHOR>
 * @date 2025/7/11 13:12
 * @since JDK 1.8
 */
@Slf4j
public class WechatCorpUtil {

    /**
     * 获取企业微信corpId
     * @param companyNoEnum
     * @return
     */
//    public static String  getCorpId(CompanyNoEnum companyNoEnum){
//        switch (companyNoEnum){
//            case HOWBUY_WEALTH:
//                return WechatConfig.wealthCorpid;
//            case HOWBUY_FUND:
//                return WechatConfig.fundCorpid;
//            default:
//                throw new BusinessException(ResponseCodeEnum.SYS_CONFIG_ERROR);
//        }
//
//    }


    /**
     * 获取企业微信corpId
     * @param corpId
     * @return
     */
//    public static CompanyNoEnum getCompanyNoEnum(String corpId){
//        if(WechatConfig.wealthCorpid.equals(corpId)){
//            return CompanyNoEnum.HOWBUY_WEALTH;
//        }
//        if(WechatConfig.fundCorpid.equals(corpId)){
//            return CompanyNoEnum.HOWBUY_FUND;
//        }
//        return null;
//    }

    /**
     * @description: 获取企业微信工具类对象
     * @param companyNoEnum
     * @return com.howbuy.crm.wechat.service.commom.utils.CorpUtilityDTO
     * @author: haoran.zhang
     * @date: 2025/7/11 15:01
     * @since JDK 1.8
     */
//    public static CorpUtilityDTO getCorpUtilityDTO(CompanyNoEnum companyNoEnum) {
//        switch (companyNoEnum){
//            case HOWBUY_WEALTH:
//                return  new CorpUtilityDTO(WechatConfig.wealthCorpid,
//                        WechatConfig.wealthToken,
//                        WechatConfig.wealthEncodingAESkey);
//            case HOWBUY_FUND:
//                return new CorpUtilityDTO(WechatConfig.fundCorpid,
//                        WechatConfig.fundToken,
//                        WechatConfig.fundEncodingAESkey);
//            default:
//                throw new BusinessException(ResponseCodeEnum.SYS_CONFIG_ERROR);
//        }
//    }


    /**
     * @description:构建WXBizMsgCrypt对象
     * @param companyNoEnum
     * @return com.howbuy.crm.wechat.service.commom.utils.WXBizMsgCrypt
     * @author: yu.zhang
     * @date: 2023/6/8 15:09
     * @since JDK 1.8
     */
//    public static WXBizMsgCrypt buildWXBizMsgCrypt(CompanyNoEnum companyNoEnum) {
//        WXBizMsgCrypt wxcpt = null;
//        try {
//            CorpUtilityDTO corpUtilityDTO = getCorpUtilityDTO(companyNoEnum);
//            wxcpt = new WXBizMsgCrypt(corpUtilityDTO.getToken(), corpUtilityDTO.getEncodingAesKey(), corpUtilityDTO.getCorpId());
//        } catch (Exception e) {
//            log.error("buildWXBizMsgCrypt Exception:{}", e.getMessage(),e);
//        }
//        return wxcpt;
//    }



    /**
     * @description: 获取应用工具类对象
     * @param wechatApplicationEnum
     * @return com.howbuy.crm.wechat.service.commom.utils.ApplicationUtilityDTO
     * @author: haoran.zhang
     * @date: 2025/7/11 15:01
     * @since JDK 1.8
     */
//    public static ApplicationUtilityDTO getApplicationUtilityDTO(WechatApplicationEnum wechatApplicationEnum) {
//        ApplicationUtilityDTO applicationUtilityDTO = new ApplicationUtilityDTO();
//
//        CompanyNoEnum companyNoEnum = wechatApplicationEnum.getCompanyNoEnum();
//
//        applicationUtilityDTO.setApplicationCode(wechatApplicationEnum.getCode());
//
//        CorpUtilityDTO  corpDto=  getCorpUtilityDTO(companyNoEnum);
//        applicationUtilityDTO.setCorpId(corpDto.getCorpId());
//        applicationUtilityDTO.setToken(corpDto.getToken());
//        applicationUtilityDTO.setEncodingAesKey(corpDto.getEncodingAesKey());
//
//        String agentId="";
//        String accessSecret;
//        // 实现逻辑，参考 ： WechatSecretEnum.secret
//        switch (wechatApplicationEnum) {
//            case WEALTH_CUSTOMER:
//                accessSecret= WechatConfig.wealthCorpSecret;
//                agentId="";
//                break;
//            case WEALTH_CRM:
//                accessSecret= WechatConfig.crmCorpSecret;
//                agentId="";
//                break;
//            case WEALTH_RHSLT:
//                accessSecret= WechatConfig.rhsltCorpSecret;
//                agentId="";
//                break;
//            case WEALTH_PORTRAIT:
//                accessSecret= WechatConfig.wealthPortraitSecret;
//                agentId=WechatConfig.wealthPortraitAgentId;
//                break;
//            case FUND_CUSTOMER:
//                accessSecret= WechatConfig.fundCorpSecret;
//                agentId="";
//                break;
//            default:
//                throw new BusinessException(ResponseCodeEnum.SYS_CONFIG_ERROR);
//        }
//        applicationUtilityDTO.setAgentId(agentId);
//        applicationUtilityDTO.setAccessSecret(accessSecret);
//        return applicationUtilityDTO;
//    }



    /**
     * 根据公司编号获取发送消息的微信应用枚举
     * @param companyNoEnum 企微-企业主体
     * @return
     */
    public static WechatApplicationEnum getSendMsgAppEnum(CompanyNoEnum companyNoEnum){
        switch (companyNoEnum){
            case HOWBUY_WEALTH:
                return WechatApplicationEnum.WEALTH_CRM;
                case HOWBUY_FUND:
                //Crocodile's TODO : 企业默认发送消息的内建应用，如需要，请自行添加
                return null;
                default:
                return null;
        }
    }



}