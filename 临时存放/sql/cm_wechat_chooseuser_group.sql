/*
 Navicat Premium Dump SQL

 Source Server         : CBH-************-3306-MySQL
 Source Server Type    : MySQL
 Source Server Version : 50729 (5.7.29-log)
 Source Host           : ************:3306
 Source Schema         : crm_wechat

 Target Server Type    : MySQL
 Target Server Version : 50729 (5.7.29-log)
 File Encoding         : 65001

 Date: 16/07/2025 13:19:27
*/

SET NAMES utf8mb4;
SET FOREIGN_KEY_CHECKS = 0;

-- ----------------------------
-- Table structure for cm_wechat_chooseuser_group
-- ----------------------------
DROP TABLE IF EXISTS `cm_wechat_chooseuser_group`;
CREATE TABLE `cm_wechat_chooseuser_group`  (
  `CHAT_ID` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '客户群id',
  `CHAT_NAME` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '客户群名称',
  `CHAT_OWNER` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '客户群-群主',
  `CHAT_FLAG` char(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '群状态 0 正常 1删除',
  `USER_ID` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '企业微信群成员ID',
  `GROUP_NICK_NAME` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '在群里的昵称',
  `NAME` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '如果是微信用户，则返回其在微信中设置的名字，如果是企业微信联系人，则返回其设置对外展示的别名或实名',
  `TYPE` varchar(10) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '客户类型 1-企业成员 2 - 外部联系人',
  `HBONE_NO` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '一账通',
  `UNIONID` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '外部联系人在微信开放平台的唯一身份标识（微信unionid）',
  `JOIN_SCENE` varchar(10) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '入群方式。1 - 由成员邀请入群（直接邀请入群） 2 - 由成员邀请入群（通过邀请链接入群）3 - 通过扫描群二维码入群',
  `USER_CHAT_FLAG` char(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '群成员状态 0在群 1退群',
  `CREATE_TIME` datetime(6) NULL DEFAULT NULL COMMENT '创建时间',
  `JOIN_TIME` datetime(6) NULL DEFAULT NULL COMMENT '入群时间',
  PRIMARY KEY (`CHAT_ID`, `USER_ID`, `USER_CHAT_FLAG`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '根据配置用户获取对应用户群的成员信息' ROW_FORMAT = DYNAMIC;

SET FOREIGN_KEY_CHECKS = 1;
