package com.howbuy.crm.wechat.service.outerservice.wechatapi;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.howbuy.crm.wechat.client.enums.CompanyNoEnum;
import com.howbuy.crm.wechat.client.enums.ResponseCodeEnum;
import com.howbuy.crm.wechat.client.enums.WechatApplicationEnum;
import com.howbuy.crm.wechat.service.commom.constant.Constants;
import com.howbuy.crm.wechat.service.commom.exception.BusinessException;
import com.howbuy.crm.wechat.service.commom.utils.ObjectUtils;
import com.howbuy.crm.wechat.service.domain.externaluser.*;
import com.howbuy.crm.wechat.service.outerservice.wechatapi.domain.ExternalWeChatBaseDTO;
import crm.howbuy.base.utils.StringUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.tuple.Pair;
import org.springframework.stereotype.Service;
import org.springframework.util.Assert;
import org.springframework.util.CollectionUtils;

import java.util.*;
import java.util.stream.Collectors;

import static com.alibaba.fastjson.JSON.parseObject;

/**
 * @description: 企业微信外部联系人API接口
 * https://developer.work.weixin.qq.com/document/path/92109
 * @author: yu.zhang
 * @date: 2023/6/19 10:50 
 * @since JDK 1.8
 * @version: 1.0
 */
@Slf4j
@Service
public class WechatExternalContactOuterService  extends AbsWechatOuterService{


    /**
     *客户联系--获取外部联系人的详细信息
     */
    private static final String GET_EXTERNAL_USER_INFO = "externalcontact/get";

    /**
     * 客户联系--企业成员[参数:userid] 所添加的  外部联系人的列表
     */
    private static final String GET_EXTERNAL_USERID_LIST = "/externalcontact/list";

    /**
     * 客户联系--修改客户备注
     */
    private static final String MODIFY_REMARK = "externalcontact/remark";

    /**
     * 客户联系--查询客户接替状态
     */
    private static final String TRANSFER_RESULT = "/externalcontact/transfer_result";


    /**
     *客户联系-- 客户群管理 获取客户群列表
     */
    private static final String GET_CHAT_GROUP_LIST = "externalcontact/groupchat/list";

    /**
     *客户联系-- 客户群管理 获取客户群详情
     */
    private static final String GET_CHAT_GROUP = "externalcontact/groupchat/get";

    /**
     * 编辑客户企业标签
     */
    private static final String MARK_TAG = "/externalcontact/mark_tag";


    /**
     * @description:* 获取客户信息
     * @param externalUserId	NOT NULL 外部联系人的userid
     * @param companyNoEnum  企微-企业主体
     * @param isSimple	是否简单信息返回。 true 忽略 relation 和profile信息
     * @return com.howbuy.crm.wechat.service.domain.externaluser.ExternalUserInfoDTO
     * @author: yu.zhang
     * @date: 2023/6/25 13:39
     * @since JDK 1.8
     */
    public ExternalUserInfoDTO getExternalUser(String externalUserId, CompanyNoEnum companyNoEnum, boolean isSimple) {
        Assert.notNull(externalUserId, "外部联系人ID不能为空");
        Map<String, String> paramMap = Maps.newHashMap();
        paramMap.put("external_userid", externalUserId);
        try {
            String externalUserInfo = requestWithCompanyNoEnum(GET_EXTERNAL_USER_INFO, companyNoEnum, paramMap, Constants.METHOD_GET);
            return transferExternalUserInfo(externalUserInfo, isSimple);
        } catch (Exception e) {
            log.error("",e);
        }
        return null;
    }

    /**
     * @description:* 获取客户信息
     * @param externalUserId	NOT NULL 外部联系人的userid
     * @param companyNoEnum  企微-企业主体
     * @return com.howbuy.crm.wechat.service.domain.externaluser.ExternalUserInfoDTO
     * @author: jin.wang03
     * @date: 2023/11/1 13:39
     * @since JDK 1.8
     */
    public ExternalUserInfoDTO getExternalUser(String externalUserId, CompanyNoEnum companyNoEnum) {
        return getExternalUser(externalUserId, companyNoEnum, false);
    }

    /**
     * @description: 获取客户群信息
     * @param empIdList	员工userid集合
     * @param companyNoEnum  企微-企业主体
     * @return java.util.List<com.howbuy.crm.wechat.service.domain.externaluser.ExternalGroupInfoDTO> 客户群信息列表
     * @author: jin.wang03
     * @date: 2023/10/31 13:30
     * @since JDK 1.8
     */
    public List<ExternalGroupInfoDTO> getChatGroupByUserIdList(List<String> empIdList, CompanyNoEnum companyNoEnum) {
        Assert.notNull(companyNoEnum, "企微-企业主体不能为空！");
        Assert.notNull(empIdList, "userId不能为空");
        List<ExternalGroupInfoDTO> resultList = new ArrayList<>();

        Map<String, Object> paramMap = Maps.newHashMap();
        Map<String, Object> userIdsMap = Maps.newHashMap();
        userIdsMap.put("userid_list", empIdList);
        paramMap.put("owner_filter", userIdsMap);
        paramMap.put("limit", 999);
        paramMap.put("status_filter", 0);
        try {
            String externalGroupRespondStr = requestWithCompanyNoEnum(GET_CHAT_GROUP_LIST, companyNoEnum, paramMap, Constants.METHOD_POST);
            while (true) {
                if (StringUtils.isEmpty(externalGroupRespondStr)) {
                    log.info("getChatGroupByUserIdList error: response is null");
                    return resultList;
                }
                JSONObject respObj = JSON.parseObject(externalGroupRespondStr);
                if (!Constants.WECHAT_ERRMSG.equals(respObj.getString("errmsg"))) {
                    log.info("getChatGroupByUserIdList error: err msg is not ok");
                    return resultList;
                }
                resultList.addAll(transferExternalGroupInfoList(respObj));

                if (!StringUtils.isEmpty(respObj.getString("next_cursor"))) {
                    paramMap.put("cursor", respObj.getString("next_cursor"));
                    externalGroupRespondStr = requestWithCompanyNoEnum(GET_CHAT_GROUP_LIST, companyNoEnum, paramMap, Constants.METHOD_POST);
                } else {
                    break;
                }
            }
        } catch (Exception e) {
            log.error("getChatGroupByUserIdList error:" + e.getMessage(), e);
        }
        return resultList;
    }



    /**
     * @description: 编辑企业外部客户标签
     * @param userId 企微用户ID
     * @param externalUserId 外部联系人ID
     * @param addTagList 新增的标签列表
     * @param removeTagList 删除的标签列表
     * @param companyNoEnum 企微-企业主体
     * @return org.apache.commons.lang3.tuple.Pair,左边是返回码，右边是返回描述
     * @author: hongdong.xie
     * @date: 2024/1/27 11:08
     * @since JDK 1.8
     */
    public Pair<String,String> markTagUsers(String userId,
                                     String externalUserId,
                                     List<String> addTagList,
                                     List<String> removeTagList,
                                     CompanyNoEnum companyNoEnum){
        if(CollectionUtils.isEmpty(addTagList) && CollectionUtils.isEmpty(removeTagList)){
            log.info("addTagList和removeTagList都为空，不执行标签操作");
            return null;
        }
        Map<String, Object> params = new HashMap<>();
        // 如果addTagList不为空，则将addTagList转成数字设置到 params 中，key 设置为 "add_tag"，value 设置为 addTagList的数组字符串
        if(!CollectionUtils.isEmpty(addTagList)){
            params.put("add_tag",addTagList);
        }

        // 如果removeTagList不为空，则将removeTagList转成数字设置到 params 中，key 设置为 "remove_tag"，value 设置为 removeTagList
        if(!CollectionUtils.isEmpty(removeTagList)){
            params.put("remove_tag",removeTagList);
        }
        params.put("userid",userId);
        params.put("external_userid",externalUserId);
        String  retMsg = requestWithCompanyNoEnum(MARK_TAG,companyNoEnum,params, Constants.METHOD_POST);
        // 解析企业微信/externalcontact/mark_tag接口返回结果
        JSONObject jsonObject = JSON.parseObject(retMsg);
        String errcode = null==jsonObject.get("errcode")?"":jsonObject.get("errcode").toString();
        String errmsg = null==jsonObject.get("errmsg")?"":jsonObject.get("errmsg").toString();

        return Pair.of(errcode,errmsg);

    }


    /**
     * 修改客户备注
     * @param userId 企微用户ID
     * @param externalUserId 外部联系人ID
     * @param remark 给客户的备注
     * @param companyNoEnum  企微-企业主体
     */
    public ExternalWeChatBaseDTO modifyRemark(String userId,
                                              String externalUserId,
                                              String remark,
                                              CompanyNoEnum companyNoEnum){

        HashMap<String, Object> map = new HashMap<>();
        map.put("userid", userId);
        map.put("external_userid", externalUserId);
        map.put("remark", remark);
        String  responseStr= requestWithCompanyNoEnum(MODIFY_REMARK, companyNoEnum, map, Constants.METHOD_POST);
        return JSON.parseObject(responseStr, ExternalWeChatBaseDTO.class);
    }

    /**
     * @description: 根据公司编码获取企业微信的corpId
     * @param companyNo 公司编码
     * @return java.lang.String
     * @author: jin.wang03
     * @date: 2023/10/31 11:20
     * @since JDK 1.8
     */
//    private static String getCorpId(String companyNo) {
//        return StringUtils.equals(companyNo, CompanyWechatEnum.WEALTH_COMPANY.getDesc()) ? WechatConfig.wealthCorpid : WechatConfig.fundCorpid;
//    }

//    public static void main(String[] args) {
//        System.out.println(getCorpId("1"));
//    }

    /**
     * @description: 转义    api返回--> service对象
     * @param respObj api返回
     * @return java.util.Collection<? extends com.howbuy.crm.wechat.service.domain.externaluser.ExternalGroupInfoDTO> 客户群信息列表
     * @author: jin.wang03
     * @date: 2023/10/31 13:31
     * @since JDK 1.8
     */
    private Collection<? extends ExternalGroupInfoDTO> transferExternalGroupInfoList(JSONObject respObj) {
        JSONArray chatGroupArray = respObj.getJSONArray("group_chat_list");
        if (CollectionUtils.isEmpty(chatGroupArray)) {
            return Lists.newArrayList();
        }
        return chatGroupArray.stream().map(chatGroup -> {
            JSONObject chatGroupObj = (JSONObject) chatGroup;
            return ExternalGroupInfoDTO.builder()
                    .chatId(ObjectUtils.trimNullString(chatGroupObj.get("chat_id")))
                    .status(ObjectUtils.trimNullString(chatGroupObj.get("status")))
                    .build();
        }).collect(Collectors.toList());
    }

    /**
     * @description:z转义    api返回--> service对象
     * @param respString
     * @param isSimple	是否简单信息返回。 true 忽略 relation 和profile信息
     * @return com.howbuy.crm.wechat.service.domain.externaluser.ExternalUserInfoDTO
     * @author: yu.zhang
     * @date: 2023/6/25 13:39
     * @since JDK 1.8
     */
    private ExternalUserInfoDTO transferExternalUserInfo(String respString, boolean isSimple) {
        JSONObject respObj;
        if (StringUtils.isEmpty(respString)) {
            return null;
        }
        respObj = parseObject(respString);
        if (respObj.isEmpty() || !respObj.containsKey("external_contact")) {
            return null;
        }
        JSONObject userObj = respObj.getJSONObject("external_contact");
        //new ExternalUserInfo
        ExternalUserInfoDTO returnInfo = new ExternalUserInfoDTO();

        String externalUserId = ObjectUtils.trimNullString(userObj.get("external_userid"));
        returnInfo.setExternalUserAvatar(ObjectUtils.trimNullString(userObj.get("avatar")))
                .setExternalUserCorpFullName(ObjectUtils.trimNullString(userObj.get("corp_full_name")))
                .setExternalUserCorpName(ObjectUtils.trimNullString(userObj.get("corp_name")))
                .setExternalUserGender(ObjectUtils.trimNullString(userObj.get("gender")))
                .setExternalUserId(externalUserId)
                .setExternalUserName(ObjectUtils.trimNullString(userObj.get("name")))
                .setExternalUserPosition(ObjectUtils.trimNullString(userObj.get("position")))
                .setExternalUserType(ObjectUtils.trimNullString(userObj.get("type")))
                .setUnionid(ObjectUtils.trimNullString(userObj.get("unionid")))
                .setWechatAvatar(ObjectUtils.trimNullString(userObj.get("avatar")));

        if (!isSimple) {
            //附属信息 关系列表
            JSONArray relationArray = respObj.getJSONArray("follow_user");
            returnInfo.getFollowUserList().addAll(transferRelationList(relationArray, externalUserId));
        }
        return returnInfo;
    }

    /**
     * @description:z转义    api返回--> 企业微信用户 vs  外部用户 关系定义
     * @param relationArray
     * @param externalUserId
     * @return java.util.List<com.howbuy.crm.wechat.service.domain.externaluser.ExternalUserRelationInfoDTO>
     * @author: yu.zhang
     * @date: 2023/6/25 13:40
     * @since JDK 1.8
     */
    private List<ExternalUserRelationInfoDTO> transferRelationList(JSONArray relationArray, String externalUserId) {
        if (relationArray.isEmpty()) {
            return Lists.newArrayList();
        }

        List<ExternalUserRelationInfoDTO> returnList = Lists.newArrayList();
        for (int index = 0; index < relationArray.size(); index++) {
            JSONObject relaionObj = relationArray.getJSONObject(index);
            String userId = ObjectUtils.trimNullString(relaionObj.get("userid"));
            ExternalUserRelationInfoDTO relationInfo = new ExternalUserRelationInfoDTO(userId, externalUserId);
            relationInfo
                    .setAddWay(ObjectUtils.trimNullString(relaionObj.get("add_way")))
                    .setCreatetime(ObjectUtils.trimNullDateMulK(relaionObj.get("createtime")))
                    .setDescription(ObjectUtils.trimNullString(relaionObj.get("description")))
                    .setOperUserid(ObjectUtils.trimNullString(relaionObj.get("oper_userid")))
                    .setRemark(ObjectUtils.trimNullString(relaionObj.get("remark")))
                    .setRemarkCorpName(ObjectUtils.trimNullString(relaionObj.get("remark_corp_name")))
                    .setState(ObjectUtils.trimNullString(relaionObj.get("state")))
            ;
            //RemarkMobiles
            JSONArray remarkMobileArray = relaionObj.getJSONArray("remark_mobiles");
            if (!remarkMobileArray.isEmpty()) {
                relationInfo.setRemarkMobiles(remarkMobileArray.toJavaList(String.class));
            }

            //TagList
            relationInfo.setTagList(transferRelationTagList(relaionObj.getJSONArray("tags")));

            returnList.add(relationInfo);
        }
        return returnList;
    }

    /**
     * @description:z转义    api返回--> 企业微信用户 vs  外部用户 关系中  tag列表
     * @param tagArray
     * @return java.util.List<com.howbuy.crm.wechat.service.domain.externaluser.UserRelationTagInfoDTO>
     * @author: yu.zhang
     * @date: 2023/6/25 13:40
     * @since JDK 1.8
     */
    private List<UserRelationTagInfoDTO> transferRelationTagList(JSONArray tagArray) {
        if (tagArray.isEmpty()) {
            return Lists.newArrayList();
        }

        List<UserRelationTagInfoDTO> tagList = Lists.newArrayList();
        for (int index = 0; index < tagArray.size(); index++) {
            JSONObject tagObj = tagArray.getJSONObject(index);
            UserRelationTagInfoDTO tagInfo = new UserRelationTagInfoDTO();
            tagInfo.setGroupName(ObjectUtils.trimNullString(tagObj.get("group_name")))
                    .setTagId(ObjectUtils.trimNullString(tagObj.get("tag_id")))
                    .setTagName(ObjectUtils.trimNullString(tagObj.get("tag_name")))
                    .setType(ObjectUtils.trimNullString(tagObj.get("type")));

            tagList.add(tagInfo);

        }
        return tagList;
    }

    /**
     * @description:(请在此添加描述)
     * @param companyNoEnum  企微-企业主体
     * @param chatId
     * @return void
     * @author: jin.wang03
     * @date: 2023/10/31 15:17
     * @since JDK 1.8
     */
    public ExternalGroupInfoDTO getGroupChatUserByChatId(CompanyNoEnum companyNoEnum, String chatId) {
        Assert.notNull(chatId, "chatId不能为空");
        Assert.notNull(companyNoEnum, "企微-企业主体不能为空！");

        Map<String, Object> paramMap = Maps.newHashMap();
        paramMap.put("chat_id", chatId);
        paramMap.put("need_name", "1");
        try {
            String externalGroupRespondStr = requestWithCompanyNoEnum(GET_CHAT_GROUP, companyNoEnum, paramMap, Constants.METHOD_POST);

            if (StringUtils.isEmpty(externalGroupRespondStr)) {
                log.info("getGroupChatUserByChatId error: response is null");
                return null;
            }
            JSONObject respObj = JSON.parseObject(externalGroupRespondStr);

            if (!Constants.WECHAT_ERRMSG.equals(respObj.getString("errmsg"))) {
                log.info("getGroupChatUserByChatId error: err msg is not ok");
                log.info("chat_id:[{}] group already dismissed", chatId);
                ExternalGroupInfoDTO groupInfoDTO = new ExternalGroupInfoDTO();
                groupInfoDTO.setErrCode(respObj.getString("errcode"));
                groupInfoDTO.setErrMsg(respObj.getString("errmsg"));
                return groupInfoDTO;
            }
            return transferExternalGroupUserList(respObj);

        } catch (Exception e) {
            log.error("getChatGroupByUserIdList error:" + e.getMessage(), e);
        }
        return null;
    }

    /**
     * @description: 转义    api返回--> service对象
     * @param respObj api返回
     * @return com.howbuy.crm.wechat.service.domain.externaluser.ExternalGroupInfoDTO 客户群信息列表
     * @author: jin.wang03
     * @date: 2023/10/31 16:43
     * @since JDK 1.8
     */
    private ExternalGroupInfoDTO transferExternalGroupUserList(JSONObject respObj) {
        JSONObject groupChat = respObj.getJSONObject("group_chat");

        ExternalGroupInfoDTO groupInfoDTO = ExternalGroupInfoDTO.builder().chatId(groupChat.getString("chat_id"))
                .name(groupChat.getString("name")).owner(groupChat.getString("owner"))
                .createTime(new Date(Long.parseLong(groupChat.getString("create_time")) * 1000))
                .notice(groupChat.getString("notice")).errCode(respObj.getString("errcode"))
                .build();

        JSONArray memberJsonArray = groupChat.getJSONArray("member_list");
        List<ExternalGroupChatUserDTO> externalGroupChatUserDTOS = memberJsonArray.stream().map(member -> {
            JSONObject memberObj = (JSONObject) member;
            return ExternalGroupChatUserDTO.builder()
                    .userId(memberObj.getString("userid"))
                    .type(memberObj.getString("type"))
                    .joinTime(new Date(Long.parseLong(memberObj.getString("join_time")) * 1000))
                    .joinScene(memberObj.getString("join_scene"))
                    .invitor(Optional.ofNullable(memberObj.getJSONObject("invitor")).orElse(new JSONObject()).getString("userid"))
                    .groupNickname(memberObj.getString("group_nickname"))
                    .name(memberObj.getString("name"))
                    .unionId(memberObj.getString("unionid"))
                    .build();
        }).collect(Collectors.toList());

        groupInfoDTO.setMemberList(externalGroupChatUserDTOS);

        JSONArray adminJsonArray = groupChat.getJSONArray("admin_list");
        List<ExternalGroupChatUserDTO> adminList = adminJsonArray.stream().map(admin -> {
            JSONObject adminObj = (JSONObject) admin;
            return ExternalGroupChatUserDTO.builder()
                    .userId(adminObj.getString("userid")).build();
        }).collect(Collectors.toList());
        groupInfoDTO.setAdminList(adminList);

        return groupInfoDTO;
    }


    /**
     * @description:(根据投顾企微账号获取跟他有好友关系的客户的ExternalUserId)
     * @param userId
     * @param companyNoEnum  企微-企业主体
     * @return java.util.List<java.lang.String>
     * @author: shuai.zhang
     * @date: 2024/3/27 14:52
     * @since JDK 1.8
     */
    public List<String> getExternalUserIdList(String userId,CompanyNoEnum companyNoEnum) {
        Assert.notNull(companyNoEnum, "企微-企业主体不能为空！");
        cn.hutool.core.lang.Assert.notNull(userId);
        Map<String,String> paramMap= Maps.newHashMap();
        paramMap.put("userid",userId);
        String respString = requestWithCompanyNoEnum(GET_EXTERNAL_USERID_LIST, companyNoEnum, paramMap, Constants.METHOD_GET);

        if(!StringUtil.isEmpty(respString)){
            JSONObject respObj=JSONObject.parseObject(respString);
            JSONArray idArray=respObj.getJSONArray("external_userid");
            if(!idArray.isEmpty()){
                return idArray.toJavaList(String.class);
            }
        }
        return Lists.newArrayList();
    }

    /**
     * @description:(获取客户划转结果)
     * @param handoverUserid	
     * @param takeoverUserid	
     * @param companyNoEnum
     * @return java.lang.String
     * @author: shuai.zhang
     * @date: 2024/3/27 18:54
     * @since JDK 1.8
     */
    public String getTransferResult(String handoverUserid,
                                    String takeoverUserid,
                                    CompanyNoEnum companyNoEnum) {
        Assert.notNull(companyNoEnum, "企微-企业主体不能为空！");
        Map<String,String> paramMap= Maps.newHashMap();
        paramMap.put("handover_userid", handoverUserid);
        paramMap.put("takeover_userid", takeoverUserid);
        String respString = requestWithCompanyNoEnum(TRANSFER_RESULT,
                companyNoEnum, paramMap, Constants.METHOD_POST);
        return respString;
    }
}
