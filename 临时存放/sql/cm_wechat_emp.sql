/*
 Navicat Premium Dump SQL

 Source Server         : CBH-************-3306-MySQL
 Source Server Type    : MySQL
 Source Server Version : 50729 (5.7.29-log)
 Source Host           : ************:3306
 Source Schema         : crm_wechat

 Target Server Type    : MySQL
 Target Server Version : 50729 (5.7.29-log)
 File Encoding         : 65001

 Date: 16/07/2025 13:20:10
*/

SET NAMES utf8mb4;
SET FOREIGN_KEY_CHECKS = 0;

-- ----------------------------
-- Table structure for cm_wechat_emp
-- ----------------------------
DROP TABLE IF EXISTS `cm_wechat_emp`;
CREATE TABLE `cm_wechat_emp`  (
  `ID` bigint(20) NOT NULL DEFAULT 0 COMMENT '主键',
  `EMP_ID` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '员工的userId',
  `EMP_NAME` varchar(150) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '员工姓名',
  `DEPT_ID` int(11) NULL DEFAULT NULL COMMENT '员工所属主部门id',
  `EMP_WORK_ID` varchar(8) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '员工工号',
  `THUMB_AVATAR` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '头像缩略图url',
  `AVATAR` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '头像url',
  `QR_CODE` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '员工个人二维码',
  `EMAIL` varchar(60) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '邮箱',
  `COMPANY_NO` varchar(2) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '企业编码1好买财富2好买基金',
  `CREATE_TIME` datetime(6) NULL DEFAULT NULL COMMENT '创建时间',
  `UPDATE_TIME` datetime(6) NULL DEFAULT NULL COMMENT '修改时间',
  `DEL_FLAG` char(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '删除标志（0 正常；1 删除）',
  `DEL_TIME` datetime(6) NULL DEFAULT NULL COMMENT '删除时间',
  PRIMARY KEY (`ID`) USING BTREE,
  INDEX `idx_emp_id`(`EMP_ID`) USING BTREE,
  INDEX `IDX_dept_id`(`DEPT_ID`) USING BTREE,
  INDEX `idx_emp_delflag`(`DEL_FLAG`) USING BTREE,
  INDEX `idx_emp_comno`(`COMPANY_NO`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '企业微信员工表' ROW_FORMAT = DYNAMIC;

SET FOREIGN_KEY_CHECKS = 1;
